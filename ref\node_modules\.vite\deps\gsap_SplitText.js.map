{"version": 3, "sources": ["../../gsap/SplitText.js"], "sourcesContent": ["/*!\n * SplitText 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2025, GreenSock. All rights reserved. Subject to the terms at https://gsap.com/standard-license.\n * @author: <PERSON>\n */\n\nlet gsap, _fonts, _coreInitted, _initIfNecessary = () => _coreInitted || SplitText.register(window.gsap), _charSegmenter = typeof Intl !== \"undefined\" ? new Intl.Segmenter() : 0, _toArray = (r) => typeof r === \"string\" ? _toArray(document.querySelectorAll(r)) : \"length\" in r ? Array.from(r) : [r], _elements = (targets) => _toArray(targets).filter((e) => e instanceof HTMLElement), _emptyArray = [], _context = function() {\n}, _spacesRegEx = /\\s+/g, _emojiSafeRegEx = new RegExp(\"\\\\p{RI}\\\\p{RI}|\\\\p{Emoji}(\\\\p{EMod}|\\\\u{FE0F}\\\\u{20E3}?|[\\\\u{E0020}-\\\\u{E007E}]+\\\\u{E007F})?(\\\\u{200D}\\\\p{Emoji}(\\\\p{EMod}|\\\\u{FE0F}\\\\u{20E3}?|[\\\\u{E0020}-\\\\u{E007E}]+\\\\u{E007F})?)*|.\", \"gu\"), _emptyBounds = { left: 0, top: 0, width: 0, height: 0 }, _stretchToFitSpecialChars = (collection, specialCharsRegEx) => {\n  if (specialCharsRegEx) {\n    let charsFound = new Set(collection.join(\"\").match(specialCharsRegEx) || _emptyArray), i = collection.length, slots, word, char, combined;\n    if (charsFound.size) {\n      while (--i > -1) {\n        word = collection[i];\n        for (char of charsFound) {\n          if (char.startsWith(word) && char.length > word.length) {\n            slots = 0;\n            combined = word;\n            while (char.startsWith(combined += collection[i + ++slots]) && combined.length < char.length) {\n            }\n            if (slots && combined.length === char.length) {\n              collection[i] = char;\n              collection.splice(i + 1, slots);\n              break;\n            }\n          }\n        }\n      }\n    }\n  }\n  return collection;\n}, _disallowInline = (element) => window.getComputedStyle(element).display === \"inline\" && (element.style.display = \"inline-block\"), _insertNodeBefore = (newChild, parent, existingChild) => parent.insertBefore(typeof newChild === \"string\" ? document.createTextNode(newChild) : newChild, existingChild), _getWrapper = (type, config, collection) => {\n  let className = config[type + \"sClass\"] || \"\", { tag = \"div\", aria = \"auto\", propIndex = false } = config, display = type === \"line\" ? \"block\" : \"inline-block\", incrementClass = className.indexOf(\"++\") > -1, wrapper = (text) => {\n    let el = document.createElement(tag), i = collection.length + 1;\n    className && (el.className = className + (incrementClass ? \" \" + className + i : \"\"));\n    propIndex && el.style.setProperty(\"--\" + type, i + \"\");\n    aria !== \"none\" && el.setAttribute(\"aria-hidden\", \"true\");\n    if (tag !== \"span\") {\n      el.style.position = \"relative\";\n      el.style.display = display;\n    }\n    el.textContent = text;\n    collection.push(el);\n    return el;\n  };\n  incrementClass && (className = className.replace(\"++\", \"\"));\n  wrapper.collection = collection;\n  return wrapper;\n}, _getLineWrapper = (element, nodes, config, collection) => {\n  let lineWrapper = _getWrapper(\"line\", config, collection), textAlign = window.getComputedStyle(element).textAlign || \"left\";\n  return (startIndex, endIndex) => {\n    let newLine = lineWrapper(\"\");\n    newLine.style.textAlign = textAlign;\n    element.insertBefore(newLine, nodes[startIndex]);\n    for (; startIndex < endIndex; startIndex++) {\n      newLine.appendChild(nodes[startIndex]);\n    }\n    newLine.normalize();\n  };\n}, _splitWordsAndCharsRecursively = (element, config, wordWrapper, charWrapper, prepForCharsOnly, deepSlice, ignore, charSplitRegEx, specialCharsRegEx, isNested) => {\n  var _a;\n  let nodes = Array.from(element.childNodes), i = 0, { wordDelimiter, reduceWhiteSpace = true, prepareText } = config, elementBounds = element.getBoundingClientRect(), lastBounds = elementBounds, isPreformatted = !reduceWhiteSpace && window.getComputedStyle(element).whiteSpace.substring(0, 3) === \"pre\", ignoredPreviousSibling = 0, wordsCollection = wordWrapper.collection, wordDelimIsNotSpace, wordDelimString, wordDelimSplitter, curNode, words, curWordEl, startsWithSpace, endsWithSpace, j, bounds, curWordChars, clonedNode, curSubNode, tempSubNode, curTextContent, wordText, lastWordText, k;\n  if (typeof wordDelimiter === \"object\") {\n    wordDelimSplitter = wordDelimiter.delimiter || wordDelimiter;\n    wordDelimString = wordDelimiter.replaceWith || \"\";\n  } else {\n    wordDelimString = wordDelimiter === \"\" ? \"\" : wordDelimiter || \" \";\n  }\n  wordDelimIsNotSpace = wordDelimString !== \" \";\n  for (; i < nodes.length; i++) {\n    curNode = nodes[i];\n    if (curNode.nodeType === 3) {\n      curTextContent = curNode.textContent || \"\";\n      if (reduceWhiteSpace) {\n        curTextContent = curTextContent.replace(_spacesRegEx, \" \");\n      } else if (isPreformatted) {\n        curTextContent = curTextContent.replace(/\\n/g, wordDelimString + \"\\n\");\n      }\n      prepareText && (curTextContent = prepareText(curTextContent, element));\n      curNode.textContent = curTextContent;\n      words = wordDelimString || wordDelimSplitter ? curTextContent.split(wordDelimSplitter || wordDelimString) : curTextContent.match(charSplitRegEx) || _emptyArray;\n      lastWordText = words[words.length - 1];\n      endsWithSpace = wordDelimIsNotSpace ? lastWordText.slice(-1) === \" \" : !lastWordText;\n      lastWordText || words.pop();\n      lastBounds = elementBounds;\n      startsWithSpace = wordDelimIsNotSpace ? words[0].charAt(0) === \" \" : !words[0];\n      startsWithSpace && _insertNodeBefore(\" \", element, curNode);\n      words[0] || words.shift();\n      _stretchToFitSpecialChars(words, specialCharsRegEx);\n      deepSlice && isNested || (curNode.textContent = \"\");\n      for (j = 1; j <= words.length; j++) {\n        wordText = words[j - 1];\n        if (!reduceWhiteSpace && isPreformatted && wordText.charAt(0) === \"\\n\") {\n          (_a = curNode.previousSibling) == null ? void 0 : _a.remove();\n          _insertNodeBefore(document.createElement(\"br\"), element, curNode);\n          wordText = wordText.slice(1);\n        }\n        if (!reduceWhiteSpace && wordText === \"\") {\n          _insertNodeBefore(wordDelimString, element, curNode);\n        } else if (wordText === \" \") {\n          element.insertBefore(document.createTextNode(\" \"), curNode);\n        } else {\n          wordDelimIsNotSpace && wordText.charAt(0) === \" \" && _insertNodeBefore(\" \", element, curNode);\n          if (ignoredPreviousSibling && j === 1 && !startsWithSpace && wordsCollection.indexOf(ignoredPreviousSibling.parentNode) > -1) {\n            curWordEl = wordsCollection[wordsCollection.length - 1];\n            curWordEl.appendChild(document.createTextNode(charWrapper ? \"\" : wordText));\n          } else {\n            curWordEl = wordWrapper(charWrapper ? \"\" : wordText);\n            _insertNodeBefore(curWordEl, element, curNode);\n            ignoredPreviousSibling && j === 1 && !startsWithSpace && curWordEl.insertBefore(ignoredPreviousSibling, curWordEl.firstChild);\n          }\n          if (charWrapper) {\n            curWordChars = _charSegmenter ? _stretchToFitSpecialChars([..._charSegmenter.segment(wordText)].map((s) => s.segment), specialCharsRegEx) : wordText.match(charSplitRegEx) || _emptyArray;\n            for (k = 0; k < curWordChars.length; k++) {\n              curWordEl.appendChild(curWordChars[k] === \" \" ? document.createTextNode(\" \") : charWrapper(curWordChars[k]));\n            }\n          }\n          if (deepSlice && isNested) {\n            curTextContent = curNode.textContent = curTextContent.substring(wordText.length + 1, curTextContent.length);\n            bounds = curWordEl.getBoundingClientRect();\n            if (bounds.top > lastBounds.top && bounds.left <= lastBounds.left) {\n              clonedNode = element.cloneNode();\n              curSubNode = element.childNodes[0];\n              while (curSubNode && curSubNode !== curWordEl) {\n                tempSubNode = curSubNode;\n                curSubNode = curSubNode.nextSibling;\n                clonedNode.appendChild(tempSubNode);\n              }\n              element.parentNode.insertBefore(clonedNode, element);\n              prepForCharsOnly && _disallowInline(clonedNode);\n            }\n            lastBounds = bounds;\n          }\n          if (j < words.length || endsWithSpace) {\n            _insertNodeBefore(j >= words.length ? \" \" : wordDelimIsNotSpace && wordText.slice(-1) === \" \" ? \" \" + wordDelimString : wordDelimString, element, curNode);\n          }\n        }\n      }\n      element.removeChild(curNode);\n      ignoredPreviousSibling = 0;\n    } else if (curNode.nodeType === 1) {\n      if (ignore && ignore.indexOf(curNode) > -1) {\n        wordsCollection.indexOf(curNode.previousSibling) > -1 && wordsCollection[wordsCollection.length - 1].appendChild(curNode);\n        ignoredPreviousSibling = curNode;\n      } else {\n        _splitWordsAndCharsRecursively(curNode, config, wordWrapper, charWrapper, prepForCharsOnly, deepSlice, ignore, charSplitRegEx, specialCharsRegEx, true);\n        ignoredPreviousSibling = 0;\n      }\n      prepForCharsOnly && _disallowInline(curNode);\n    }\n  }\n};\nconst _SplitText = class _SplitText {\n  constructor(elements, config) {\n    this.isSplit = false;\n    _initIfNecessary();\n    this.elements = _elements(elements);\n    this.chars = [];\n    this.words = [];\n    this.lines = [];\n    this.masks = [];\n    this.vars = config;\n    this._split = () => this.isSplit && this.split(this.vars);\n    let orig = [], timerId, checkWidths = () => {\n      let i = orig.length, o;\n      while (i--) {\n        o = orig[i];\n        let w = o.element.offsetWidth;\n        if (w !== o.width) {\n          o.width = w;\n          this._split();\n          return;\n        }\n      }\n    };\n    this._data = { orig, obs: typeof ResizeObserver !== \"undefined\" && new ResizeObserver(() => {\n      clearTimeout(timerId);\n      timerId = setTimeout(checkWidths, 200);\n    }) };\n    _context(this);\n    this.split(config);\n  }\n  split(config) {\n    this.isSplit && this.revert();\n    this.vars = config = config || this.vars || {};\n    let { type = \"chars,words,lines\", aria = \"auto\", deepSlice = true, smartWrap, onSplit, autoSplit = false, specialChars, mask } = this.vars, splitLines = type.indexOf(\"lines\") > -1, splitCharacters = type.indexOf(\"chars\") > -1, splitWords = type.indexOf(\"words\") > -1, onlySplitCharacters = splitCharacters && !splitWords && !splitLines, specialCharsRegEx = specialChars && (\"push\" in specialChars ? new RegExp(\"(?:\" + specialChars.join(\"|\") + \")\", \"gu\") : specialChars), finalCharSplitRegEx = specialCharsRegEx ? new RegExp(specialCharsRegEx.source + \"|\" + _emojiSafeRegEx.source, \"gu\") : _emojiSafeRegEx, ignore = !!config.ignore && _elements(config.ignore), { orig, animTime, obs } = this._data, onSplitResult;\n    if (splitCharacters || splitWords || splitLines) {\n      this.elements.forEach((element, index) => {\n        orig[index] = {\n          element,\n          html: element.innerHTML,\n          ariaL: element.getAttribute(\"aria-label\"),\n          ariaH: element.getAttribute(\"aria-hidden\")\n        };\n        aria === \"auto\" ? element.setAttribute(\"aria-label\", (element.textContent || \"\").trim()) : aria === \"hidden\" && element.setAttribute(\"aria-hidden\", \"true\");\n        let chars = [], words = [], lines = [], charWrapper = splitCharacters ? _getWrapper(\"char\", config, chars) : null, wordWrapper = _getWrapper(\"word\", config, words), i, curWord, smartWrapSpan, nextSibling;\n        _splitWordsAndCharsRecursively(element, config, wordWrapper, charWrapper, onlySplitCharacters, deepSlice && (splitLines || onlySplitCharacters), ignore, finalCharSplitRegEx, specialCharsRegEx, false);\n        if (splitLines) {\n          let nodes = _toArray(element.childNodes), wrapLine = _getLineWrapper(element, nodes, config, lines), curNode, toRemove = [], lineStartIndex = 0, allBounds = nodes.map((n) => n.nodeType === 1 ? n.getBoundingClientRect() : _emptyBounds), lastBounds = _emptyBounds;\n          for (i = 0; i < nodes.length; i++) {\n            curNode = nodes[i];\n            if (curNode.nodeType === 1) {\n              if (curNode.nodeName === \"BR\") {\n                toRemove.push(curNode);\n                wrapLine(lineStartIndex, i + 1);\n                lineStartIndex = i + 1;\n                lastBounds = allBounds[lineStartIndex];\n              } else {\n                if (i && allBounds[i].top > lastBounds.top && allBounds[i].left <= lastBounds.left) {\n                  wrapLine(lineStartIndex, i);\n                  lineStartIndex = i;\n                }\n                lastBounds = allBounds[i];\n              }\n            }\n          }\n          lineStartIndex < i && wrapLine(lineStartIndex, i);\n          toRemove.forEach((el) => {\n            var _a;\n            return (_a = el.parentNode) == null ? void 0 : _a.removeChild(el);\n          });\n        }\n        if (!splitWords) {\n          for (i = 0; i < words.length; i++) {\n            curWord = words[i];\n            if (splitCharacters || !curWord.nextSibling || curWord.nextSibling.nodeType !== 3) {\n              if (smartWrap && !splitLines) {\n                smartWrapSpan = document.createElement(\"span\");\n                smartWrapSpan.style.whiteSpace = \"nowrap\";\n                while (curWord.firstChild) {\n                  smartWrapSpan.appendChild(curWord.firstChild);\n                }\n                curWord.replaceWith(smartWrapSpan);\n              } else {\n                curWord.replaceWith(...curWord.childNodes);\n              }\n            } else {\n              nextSibling = curWord.nextSibling;\n              if (nextSibling && nextSibling.nodeType === 3) {\n                nextSibling.textContent = (curWord.textContent || \"\") + (nextSibling.textContent || \"\");\n                curWord.remove();\n              }\n            }\n          }\n          words.length = 0;\n          element.normalize();\n        }\n        this.lines.push(...lines);\n        this.words.push(...words);\n        this.chars.push(...chars);\n      });\n      mask && this[mask] && this.masks.push(...this[mask].map((el) => {\n        let maskEl = el.cloneNode();\n        el.replaceWith(maskEl);\n        maskEl.appendChild(el);\n        el.className && (maskEl.className = el.className.replace(/(\\b\\w+\\b)/g, \"$1-mask\"));\n        maskEl.style.overflow = \"clip\";\n        return maskEl;\n      }));\n    }\n    this.isSplit = true;\n    _fonts && (autoSplit ? _fonts.addEventListener(\"loadingdone\", this._split) : _fonts.status === \"loading\" && console.warn(\"SplitText called before fonts loaded\"));\n    if ((onSplitResult = onSplit && onSplit(this)) && onSplitResult.totalTime) {\n      this._data.anim = animTime ? onSplitResult.totalTime(animTime) : onSplitResult;\n    }\n    splitLines && autoSplit && this.elements.forEach((element, index) => {\n      orig[index].width = element.offsetWidth;\n      obs && obs.observe(element);\n    });\n    return this;\n  }\n  revert() {\n    var _a, _b;\n    let { orig, anim, obs } = this._data;\n    obs && obs.disconnect();\n    orig.forEach(({ element, html, ariaL, ariaH }) => {\n      element.innerHTML = html;\n      ariaL ? element.setAttribute(\"aria-label\", ariaL) : element.removeAttribute(\"aria-label\");\n      ariaH ? element.setAttribute(\"aria-hidden\", ariaH) : element.removeAttribute(\"aria-hidden\");\n    });\n    this.chars.length = this.words.length = this.lines.length = orig.length = this.masks.length = 0;\n    this.isSplit = false;\n    _fonts == null ? void 0 : _fonts.removeEventListener(\"loadingdone\", this._split);\n    if (anim) {\n      this._data.animTime = anim.totalTime();\n      anim.revert();\n    }\n    (_b = (_a = this.vars).onRevert) == null ? void 0 : _b.call(_a, this);\n    return this;\n  }\n  static create(elements, config) {\n    return new _SplitText(elements, config);\n  }\n  static register(core) {\n    gsap = gsap || core || window.gsap;\n    if (gsap) {\n      _toArray = gsap.utils.toArray;\n      _context = gsap.core.context || _context;\n    }\n    if (!_coreInitted && window.innerWidth > 0) {\n      _fonts = document.fonts;\n      _coreInitted = true;\n    }\n  }\n};\n_SplitText.version = \"3.13.0\";\nlet SplitText = _SplitText;\n\nexport { SplitText, SplitText as default };\n"], "mappings": ";;;AAQA,IAAI;AAAJ,IAAU;AAAV,IAAkB;AAAlB,IAAgC,mBAAmB,MAAM,gBAAgB,UAAU,SAAS,OAAO,IAAI;AAAvG,IAA0G,iBAAiB,OAAO,SAAS,cAAc,IAAI,KAAK,UAAU,IAAI;AAAhL,IAAmL,WAAW,CAAC,MAAM,OAAO,MAAM,WAAW,SAAS,SAAS,iBAAiB,CAAC,CAAC,IAAI,YAAY,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;AAAxS,IAA2S,YAAY,CAAC,YAAY,SAAS,OAAO,EAAE,OAAO,CAAC,MAAM,aAAa,WAAW;AAA5X,IAA+X,cAAc,CAAC;AAA9Y,IAAiZ,WAAW,WAAW;AACva;AADA,IACG,eAAe;AADlB,IAC0B,kBAAkB,IAAI,OAAO,2LAA2L,IAAI;AADtP,IACyP,eAAe,EAAE,MAAM,GAAG,KAAK,GAAG,OAAO,GAAG,QAAQ,EAAE;AAD/S,IACkT,4BAA4B,CAAC,YAAY,sBAAsB;AAC/W,MAAI,mBAAmB;AACrB,QAAI,aAAa,IAAI,IAAI,WAAW,KAAK,EAAE,EAAE,MAAM,iBAAiB,KAAK,WAAW,GAAG,IAAI,WAAW,QAAQ,OAAO,MAAM,MAAM;AACjI,QAAI,WAAW,MAAM;AACnB,aAAO,EAAE,IAAI,IAAI;AACf,eAAO,WAAW,CAAC;AACnB,aAAK,QAAQ,YAAY;AACvB,cAAI,KAAK,WAAW,IAAI,KAAK,KAAK,SAAS,KAAK,QAAQ;AACtD,oBAAQ;AACR,uBAAW;AACX,mBAAO,KAAK,WAAW,YAAY,WAAW,IAAI,EAAE,KAAK,CAAC,KAAK,SAAS,SAAS,KAAK,QAAQ;AAAA,YAC9F;AACA,gBAAI,SAAS,SAAS,WAAW,KAAK,QAAQ;AAC5C,yBAAW,CAAC,IAAI;AAChB,yBAAW,OAAO,IAAI,GAAG,KAAK;AAC9B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAxBA,IAwBG,kBAAkB,CAAC,YAAY,OAAO,iBAAiB,OAAO,EAAE,YAAY,aAAa,QAAQ,MAAM,UAAU;AAxBpH,IAwBqI,oBAAoB,CAAC,UAAU,QAAQ,kBAAkB,OAAO,aAAa,OAAO,aAAa,WAAW,SAAS,eAAe,QAAQ,IAAI,UAAU,aAAa;AAxB5S,IAwB+S,cAAc,CAAC,MAAM,QAAQ,eAAe;AACzV,MAAI,YAAY,OAAO,OAAO,QAAQ,KAAK,IAAI,EAAE,MAAM,OAAO,OAAO,QAAQ,YAAY,MAAM,IAAI,QAAQ,UAAU,SAAS,SAAS,UAAU,gBAAgB,iBAAiB,UAAU,QAAQ,IAAI,IAAI,IAAI,UAAU,CAAC,SAAS;AAClO,QAAI,KAAK,SAAS,cAAc,GAAG,GAAG,IAAI,WAAW,SAAS;AAC9D,kBAAc,GAAG,YAAY,aAAa,iBAAiB,MAAM,YAAY,IAAI;AACjF,iBAAa,GAAG,MAAM,YAAY,OAAO,MAAM,IAAI,EAAE;AACrD,aAAS,UAAU,GAAG,aAAa,eAAe,MAAM;AACxD,QAAI,QAAQ,QAAQ;AAClB,SAAG,MAAM,WAAW;AACpB,SAAG,MAAM,UAAU;AAAA,IACrB;AACA,OAAG,cAAc;AACjB,eAAW,KAAK,EAAE;AAClB,WAAO;AAAA,EACT;AACA,qBAAmB,YAAY,UAAU,QAAQ,MAAM,EAAE;AACzD,UAAQ,aAAa;AACrB,SAAO;AACT;AAzCA,IAyCG,kBAAkB,CAAC,SAAS,OAAO,QAAQ,eAAe;AAC3D,MAAI,cAAc,YAAY,QAAQ,QAAQ,UAAU,GAAG,YAAY,OAAO,iBAAiB,OAAO,EAAE,aAAa;AACrH,SAAO,CAAC,YAAY,aAAa;AAC/B,QAAI,UAAU,YAAY,EAAE;AAC5B,YAAQ,MAAM,YAAY;AAC1B,YAAQ,aAAa,SAAS,MAAM,UAAU,CAAC;AAC/C,WAAO,aAAa,UAAU,cAAc;AAC1C,cAAQ,YAAY,MAAM,UAAU,CAAC;AAAA,IACvC;AACA,YAAQ,UAAU;AAAA,EACpB;AACF;AApDA,IAoDG,iCAAiC,CAAC,SAAS,QAAQ,aAAa,aAAa,kBAAkB,WAAW,QAAQ,gBAAgB,mBAAmB,aAAa;AACnK,MAAI;AACJ,MAAI,QAAQ,MAAM,KAAK,QAAQ,UAAU,GAAG,IAAI,GAAG,EAAE,eAAe,mBAAmB,MAAM,YAAY,IAAI,QAAQ,gBAAgB,QAAQ,sBAAsB,GAAG,aAAa,eAAe,iBAAiB,CAAC,oBAAoB,OAAO,iBAAiB,OAAO,EAAE,WAAW,UAAU,GAAG,CAAC,MAAM,OAAO,yBAAyB,GAAG,kBAAkB,YAAY,YAAY,qBAAqB,iBAAiB,mBAAmB,SAAS,OAAO,WAAW,iBAAiB,eAAe,GAAG,QAAQ,cAAc,YAAY,YAAY,aAAa,gBAAgB,UAAU,cAAc;AAC/kB,MAAI,OAAO,kBAAkB,UAAU;AACrC,wBAAoB,cAAc,aAAa;AAC/C,sBAAkB,cAAc,eAAe;AAAA,EACjD,OAAO;AACL,sBAAkB,kBAAkB,KAAK,KAAK,iBAAiB;AAAA,EACjE;AACA,wBAAsB,oBAAoB;AAC1C,SAAO,IAAI,MAAM,QAAQ,KAAK;AAC5B,cAAU,MAAM,CAAC;AACjB,QAAI,QAAQ,aAAa,GAAG;AAC1B,uBAAiB,QAAQ,eAAe;AACxC,UAAI,kBAAkB;AACpB,yBAAiB,eAAe,QAAQ,cAAc,GAAG;AAAA,MAC3D,WAAW,gBAAgB;AACzB,yBAAiB,eAAe,QAAQ,OAAO,kBAAkB,IAAI;AAAA,MACvE;AACA,sBAAgB,iBAAiB,YAAY,gBAAgB,OAAO;AACpE,cAAQ,cAAc;AACtB,cAAQ,mBAAmB,oBAAoB,eAAe,MAAM,qBAAqB,eAAe,IAAI,eAAe,MAAM,cAAc,KAAK;AACpJ,qBAAe,MAAM,MAAM,SAAS,CAAC;AACrC,sBAAgB,sBAAsB,aAAa,MAAM,EAAE,MAAM,MAAM,CAAC;AACxE,sBAAgB,MAAM,IAAI;AAC1B,mBAAa;AACb,wBAAkB,sBAAsB,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC;AAC7E,yBAAmB,kBAAkB,KAAK,SAAS,OAAO;AAC1D,YAAM,CAAC,KAAK,MAAM,MAAM;AACxB,gCAA0B,OAAO,iBAAiB;AAClD,mBAAa,aAAa,QAAQ,cAAc;AAChD,WAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,KAAK;AAClC,mBAAW,MAAM,IAAI,CAAC;AACtB,YAAI,CAAC,oBAAoB,kBAAkB,SAAS,OAAO,CAAC,MAAM,MAAM;AACtE,WAAC,KAAK,QAAQ,oBAAoB,OAAO,SAAS,GAAG,OAAO;AAC5D,4BAAkB,SAAS,cAAc,IAAI,GAAG,SAAS,OAAO;AAChE,qBAAW,SAAS,MAAM,CAAC;AAAA,QAC7B;AACA,YAAI,CAAC,oBAAoB,aAAa,IAAI;AACxC,4BAAkB,iBAAiB,SAAS,OAAO;AAAA,QACrD,WAAW,aAAa,KAAK;AAC3B,kBAAQ,aAAa,SAAS,eAAe,GAAG,GAAG,OAAO;AAAA,QAC5D,OAAO;AACL,iCAAuB,SAAS,OAAO,CAAC,MAAM,OAAO,kBAAkB,KAAK,SAAS,OAAO;AAC5F,cAAI,0BAA0B,MAAM,KAAK,CAAC,mBAAmB,gBAAgB,QAAQ,uBAAuB,UAAU,IAAI,IAAI;AAC5H,wBAAY,gBAAgB,gBAAgB,SAAS,CAAC;AACtD,sBAAU,YAAY,SAAS,eAAe,cAAc,KAAK,QAAQ,CAAC;AAAA,UAC5E,OAAO;AACL,wBAAY,YAAY,cAAc,KAAK,QAAQ;AACnD,8BAAkB,WAAW,SAAS,OAAO;AAC7C,sCAA0B,MAAM,KAAK,CAAC,mBAAmB,UAAU,aAAa,wBAAwB,UAAU,UAAU;AAAA,UAC9H;AACA,cAAI,aAAa;AACf,2BAAe,iBAAiB,0BAA0B,CAAC,GAAG,eAAe,QAAQ,QAAQ,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,GAAG,iBAAiB,IAAI,SAAS,MAAM,cAAc,KAAK;AAC9K,iBAAK,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AACxC,wBAAU,YAAY,aAAa,CAAC,MAAM,MAAM,SAAS,eAAe,GAAG,IAAI,YAAY,aAAa,CAAC,CAAC,CAAC;AAAA,YAC7G;AAAA,UACF;AACA,cAAI,aAAa,UAAU;AACzB,6BAAiB,QAAQ,cAAc,eAAe,UAAU,SAAS,SAAS,GAAG,eAAe,MAAM;AAC1G,qBAAS,UAAU,sBAAsB;AACzC,gBAAI,OAAO,MAAM,WAAW,OAAO,OAAO,QAAQ,WAAW,MAAM;AACjE,2BAAa,QAAQ,UAAU;AAC/B,2BAAa,QAAQ,WAAW,CAAC;AACjC,qBAAO,cAAc,eAAe,WAAW;AAC7C,8BAAc;AACd,6BAAa,WAAW;AACxB,2BAAW,YAAY,WAAW;AAAA,cACpC;AACA,sBAAQ,WAAW,aAAa,YAAY,OAAO;AACnD,kCAAoB,gBAAgB,UAAU;AAAA,YAChD;AACA,yBAAa;AAAA,UACf;AACA,cAAI,IAAI,MAAM,UAAU,eAAe;AACrC,8BAAkB,KAAK,MAAM,SAAS,MAAM,uBAAuB,SAAS,MAAM,EAAE,MAAM,MAAM,MAAM,kBAAkB,iBAAiB,SAAS,OAAO;AAAA,UAC3J;AAAA,QACF;AAAA,MACF;AACA,cAAQ,YAAY,OAAO;AAC3B,+BAAyB;AAAA,IAC3B,WAAW,QAAQ,aAAa,GAAG;AACjC,UAAI,UAAU,OAAO,QAAQ,OAAO,IAAI,IAAI;AAC1C,wBAAgB,QAAQ,QAAQ,eAAe,IAAI,MAAM,gBAAgB,gBAAgB,SAAS,CAAC,EAAE,YAAY,OAAO;AACxH,iCAAyB;AAAA,MAC3B,OAAO;AACL,uCAA+B,SAAS,QAAQ,aAAa,aAAa,kBAAkB,WAAW,QAAQ,gBAAgB,mBAAmB,IAAI;AACtJ,iCAAyB;AAAA,MAC3B;AACA,0BAAoB,gBAAgB,OAAO;AAAA,IAC7C;AAAA,EACF;AACF;AACA,IAAM,aAAa,MAAMA,YAAW;AAAA,EAClC,YAAY,UAAU,QAAQ;AAC5B,SAAK,UAAU;AACf,qBAAiB;AACjB,SAAK,WAAW,UAAU,QAAQ;AAClC,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,CAAC;AACd,SAAK,OAAO;AACZ,SAAK,SAAS,MAAM,KAAK,WAAW,KAAK,MAAM,KAAK,IAAI;AACxD,QAAI,OAAO,CAAC,GAAG,SAAS,cAAc,MAAM;AAC1C,UAAI,IAAI,KAAK,QAAQ;AACrB,aAAO,KAAK;AACV,YAAI,KAAK,CAAC;AACV,YAAI,IAAI,EAAE,QAAQ;AAClB,YAAI,MAAM,EAAE,OAAO;AACjB,YAAE,QAAQ;AACV,eAAK,OAAO;AACZ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,QAAQ,EAAE,MAAM,KAAK,OAAO,mBAAmB,eAAe,IAAI,eAAe,MAAM;AAC1F,mBAAa,OAAO;AACpB,gBAAU,WAAW,aAAa,GAAG;AAAA,IACvC,CAAC,EAAE;AACH,aAAS,IAAI;AACb,SAAK,MAAM,MAAM;AAAA,EACnB;AAAA,EACA,MAAM,QAAQ;AACZ,SAAK,WAAW,KAAK,OAAO;AAC5B,SAAK,OAAO,SAAS,UAAU,KAAK,QAAQ,CAAC;AAC7C,QAAI,EAAE,OAAO,qBAAqB,OAAO,QAAQ,YAAY,MAAM,WAAW,SAAS,YAAY,OAAO,cAAc,KAAK,IAAI,KAAK,MAAM,aAAa,KAAK,QAAQ,OAAO,IAAI,IAAI,kBAAkB,KAAK,QAAQ,OAAO,IAAI,IAAI,aAAa,KAAK,QAAQ,OAAO,IAAI,IAAI,sBAAsB,mBAAmB,CAAC,cAAc,CAAC,YAAY,oBAAoB,iBAAiB,UAAU,eAAe,IAAI,OAAO,QAAQ,aAAa,KAAK,GAAG,IAAI,KAAK,IAAI,IAAI,eAAe,sBAAsB,oBAAoB,IAAI,OAAO,kBAAkB,SAAS,MAAM,gBAAgB,QAAQ,IAAI,IAAI,iBAAiB,SAAS,CAAC,CAAC,OAAO,UAAU,UAAU,OAAO,MAAM,GAAG,EAAE,MAAM,UAAU,IAAI,IAAI,KAAK,OAAO;AAC1rB,QAAI,mBAAmB,cAAc,YAAY;AAC/C,WAAK,SAAS,QAAQ,CAAC,SAAS,UAAU;AACxC,aAAK,KAAK,IAAI;AAAA,UACZ;AAAA,UACA,MAAM,QAAQ;AAAA,UACd,OAAO,QAAQ,aAAa,YAAY;AAAA,UACxC,OAAO,QAAQ,aAAa,aAAa;AAAA,QAC3C;AACA,iBAAS,SAAS,QAAQ,aAAa,eAAe,QAAQ,eAAe,IAAI,KAAK,CAAC,IAAI,SAAS,YAAY,QAAQ,aAAa,eAAe,MAAM;AAC1J,YAAI,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,cAAc,kBAAkB,YAAY,QAAQ,QAAQ,KAAK,IAAI,MAAM,cAAc,YAAY,QAAQ,QAAQ,KAAK,GAAG,GAAG,SAAS,eAAe;AAChM,uCAA+B,SAAS,QAAQ,aAAa,aAAa,qBAAqB,cAAc,cAAc,sBAAsB,QAAQ,qBAAqB,mBAAmB,KAAK;AACtM,YAAI,YAAY;AACd,cAAI,QAAQ,SAAS,QAAQ,UAAU,GAAG,WAAW,gBAAgB,SAAS,OAAO,QAAQ,KAAK,GAAG,SAAS,WAAW,CAAC,GAAG,iBAAiB,GAAG,YAAY,MAAM,IAAI,CAAC,MAAM,EAAE,aAAa,IAAI,EAAE,sBAAsB,IAAI,YAAY,GAAG,aAAa;AACzP,eAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,sBAAU,MAAM,CAAC;AACjB,gBAAI,QAAQ,aAAa,GAAG;AAC1B,kBAAI,QAAQ,aAAa,MAAM;AAC7B,yBAAS,KAAK,OAAO;AACrB,yBAAS,gBAAgB,IAAI,CAAC;AAC9B,iCAAiB,IAAI;AACrB,6BAAa,UAAU,cAAc;AAAA,cACvC,OAAO;AACL,oBAAI,KAAK,UAAU,CAAC,EAAE,MAAM,WAAW,OAAO,UAAU,CAAC,EAAE,QAAQ,WAAW,MAAM;AAClF,2BAAS,gBAAgB,CAAC;AAC1B,mCAAiB;AAAA,gBACnB;AACA,6BAAa,UAAU,CAAC;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AACA,2BAAiB,KAAK,SAAS,gBAAgB,CAAC;AAChD,mBAAS,QAAQ,CAAC,OAAO;AACvB,gBAAI;AACJ,oBAAQ,KAAK,GAAG,eAAe,OAAO,SAAS,GAAG,YAAY,EAAE;AAAA,UAClE,CAAC;AAAA,QACH;AACA,YAAI,CAAC,YAAY;AACf,eAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,sBAAU,MAAM,CAAC;AACjB,gBAAI,mBAAmB,CAAC,QAAQ,eAAe,QAAQ,YAAY,aAAa,GAAG;AACjF,kBAAI,aAAa,CAAC,YAAY;AAC5B,gCAAgB,SAAS,cAAc,MAAM;AAC7C,8BAAc,MAAM,aAAa;AACjC,uBAAO,QAAQ,YAAY;AACzB,gCAAc,YAAY,QAAQ,UAAU;AAAA,gBAC9C;AACA,wBAAQ,YAAY,aAAa;AAAA,cACnC,OAAO;AACL,wBAAQ,YAAY,GAAG,QAAQ,UAAU;AAAA,cAC3C;AAAA,YACF,OAAO;AACL,4BAAc,QAAQ;AACtB,kBAAI,eAAe,YAAY,aAAa,GAAG;AAC7C,4BAAY,eAAe,QAAQ,eAAe,OAAO,YAAY,eAAe;AACpF,wBAAQ,OAAO;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AACA,gBAAM,SAAS;AACf,kBAAQ,UAAU;AAAA,QACpB;AACA,aAAK,MAAM,KAAK,GAAG,KAAK;AACxB,aAAK,MAAM,KAAK,GAAG,KAAK;AACxB,aAAK,MAAM,KAAK,GAAG,KAAK;AAAA,MAC1B,CAAC;AACD,cAAQ,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,GAAG,KAAK,IAAI,EAAE,IAAI,CAAC,OAAO;AAC9D,YAAI,SAAS,GAAG,UAAU;AAC1B,WAAG,YAAY,MAAM;AACrB,eAAO,YAAY,EAAE;AACrB,WAAG,cAAc,OAAO,YAAY,GAAG,UAAU,QAAQ,cAAc,SAAS;AAChF,eAAO,MAAM,WAAW;AACxB,eAAO;AAAA,MACT,CAAC,CAAC;AAAA,IACJ;AACA,SAAK,UAAU;AACf,eAAW,YAAY,OAAO,iBAAiB,eAAe,KAAK,MAAM,IAAI,OAAO,WAAW,aAAa,QAAQ,KAAK,sCAAsC;AAC/J,SAAK,gBAAgB,WAAW,QAAQ,IAAI,MAAM,cAAc,WAAW;AACzE,WAAK,MAAM,OAAO,WAAW,cAAc,UAAU,QAAQ,IAAI;AAAA,IACnE;AACA,kBAAc,aAAa,KAAK,SAAS,QAAQ,CAAC,SAAS,UAAU;AACnE,WAAK,KAAK,EAAE,QAAQ,QAAQ;AAC5B,aAAO,IAAI,QAAQ,OAAO;AAAA,IAC5B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,QAAI,IAAI;AACR,QAAI,EAAE,MAAM,MAAM,IAAI,IAAI,KAAK;AAC/B,WAAO,IAAI,WAAW;AACtB,SAAK,QAAQ,CAAC,EAAE,SAAS,MAAM,OAAO,MAAM,MAAM;AAChD,cAAQ,YAAY;AACpB,cAAQ,QAAQ,aAAa,cAAc,KAAK,IAAI,QAAQ,gBAAgB,YAAY;AACxF,cAAQ,QAAQ,aAAa,eAAe,KAAK,IAAI,QAAQ,gBAAgB,aAAa;AAAA,IAC5F,CAAC;AACD,SAAK,MAAM,SAAS,KAAK,MAAM,SAAS,KAAK,MAAM,SAAS,KAAK,SAAS,KAAK,MAAM,SAAS;AAC9F,SAAK,UAAU;AACf,cAAU,OAAO,SAAS,OAAO,oBAAoB,eAAe,KAAK,MAAM;AAC/E,QAAI,MAAM;AACR,WAAK,MAAM,WAAW,KAAK,UAAU;AACrC,WAAK,OAAO;AAAA,IACd;AACA,KAAC,MAAM,KAAK,KAAK,MAAM,aAAa,OAAO,SAAS,GAAG,KAAK,IAAI,IAAI;AACpE,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,UAAU,QAAQ;AAC9B,WAAO,IAAIA,YAAW,UAAU,MAAM;AAAA,EACxC;AAAA,EACA,OAAO,SAAS,MAAM;AACpB,WAAO,QAAQ,QAAQ,OAAO;AAC9B,QAAI,MAAM;AACR,iBAAW,KAAK,MAAM;AACtB,iBAAW,KAAK,KAAK,WAAW;AAAA,IAClC;AACA,QAAI,CAAC,gBAAgB,OAAO,aAAa,GAAG;AAC1C,eAAS,SAAS;AAClB,qBAAe;AAAA,IACjB;AAAA,EACF;AACF;AACA,WAAW,UAAU;AACrB,IAAI,YAAY;", "names": ["_SplitText"]}