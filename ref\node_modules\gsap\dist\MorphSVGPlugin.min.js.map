{"version": 3, "file": "MorphSVGPlugin.min.js", "sources": ["../src/utils/paths.js", "../src/MorphSVGPlugin.js"], "sourcesContent": ["/*!\n * paths 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n\t_numbersExp = /(?:(-)?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n\t_scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n\t_selectorExp = /(^[#\\.][a-z]|[a-y][a-z])/i,\n\t_DEG2RAD = Math.PI / 180,\n\t_RAD2DEG = 180 / Math.PI,\n\t_sin = Math.sin,\n\t_cos = Math.cos,\n\t_abs = Math.abs,\n\t_sqrt = Math.sqrt,\n\t_atan2 = Math.atan2,\n\t_largeNum = 1e8,\n\t_isString = value => typeof(value) === \"string\",\n\t_isNumber = value => typeof(value) === \"number\",\n\t_isUndefined = value => typeof(value) === \"undefined\",\n\t_temp = {},\n\t_temp2 = {},\n\t_roundingNum = 1e5,\n\t_wrapProgress = progress => (Math.round((progress + _largeNum) % 1 * _roundingNum) / _roundingNum) || ((progress < 0) ? 0 : 1), //if progress lands on 1, the % will make it 0 which is why we || 1, but not if it's negative because it makes more sense for motion to end at 0 in that case.\n\t_round = value => (Math.round(value * _roundingNum) / _roundingNum) || 0,\n\t_roundPrecise = value => (Math.round(value * 1e10) / 1e10) || 0,\n\t_splitSegment = (rawPath, segIndex, i, t) => {\n\t\tlet segment = rawPath[segIndex],\n\t\t\tshift = t === 1 ? 6 : subdivideSegment(segment, i, t);\n\t\tif ((shift || !t) && shift + i + 2 < segment.length) {\n\t\t\trawPath.splice(segIndex, 0, segment.slice(0, i + shift + 2));\n\t\t\tsegment.splice(0, i + shift);\n\t\t\treturn 1;\n\t\t}\n\t},\n\t_getSampleIndex = (samples, length, progress) => {\n\t\t// slightly slower way than doing this (when there's no lookup): segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0;\n\t\tlet l = samples.length,\n\t\t\ti = ~~(progress * l);\n\t\tif (samples[i] > length) {\n\t\t\twhile (--i && samples[i] > length) {}\n\t\t\ti < 0 && (i = 0);\n\t\t} else {\n\t\t\twhile (samples[++i] < length && i < l) {}\n\t\t}\n\t\treturn i < l ? i : l - 1;\n\t},\n\t_reverseRawPath = (rawPath, skipOuter) => {\n\t\tlet i = rawPath.length;\n\t\tskipOuter || rawPath.reverse();\n\t\twhile (i--) {\n\t\t\trawPath[i].reversed || reverseSegment(rawPath[i]);\n\t\t}\n\t},\n\t_copyMetaData = (source, copy) => {\n\t\tcopy.totalLength = source.totalLength;\n\t\tif (source.samples) { //segment\n\t\t\tcopy.samples = source.samples.slice(0);\n\t\t\tcopy.lookup = source.lookup.slice(0);\n\t\t\tcopy.minLength = source.minLength;\n\t\t\tcopy.resolution = source.resolution;\n\t\t} else if (source.totalPoints) { //rawPath\n\t\t\tcopy.totalPoints = source.totalPoints;\n\t\t}\n\t\treturn copy;\n\t},\n\t//pushes a new segment into a rawPath, but if its starting values match the ending values of the last segment, it'll merge it into that same segment (to reduce the number of segments)\n\t_appendOrMerge = (rawPath, segment) => {\n\t\tlet index = rawPath.length,\n\t\t\tprevSeg = rawPath[index - 1] || [],\n\t\t\tl = prevSeg.length;\n\t\tif (index && segment[0] === prevSeg[l-2] && segment[1] === prevSeg[l-1]) {\n\t\t\tsegment = prevSeg.concat(segment.slice(2));\n\t\t\tindex--;\n\t\t}\n\t\trawPath[index] = segment;\n\t},\n\t_bestDistance;\n\n/* TERMINOLOGY\n - RawPath - an array of arrays, one for each Segment. A single RawPath could have multiple \"M\" commands, defining Segments (paths aren't always connected).\n - Segment - an array containing a sequence of Cubic Bezier coordinates in alternating x, y, x, y format. Starting anchor, then control point 1, control point 2, and ending anchor, then the next control point 1, control point 2, anchor, etc. Uses less memory than an array with a bunch of {x, y} points.\n - Bezier - a single cubic Bezier with a starting anchor, two control points, and an ending anchor.\n - the variable \"t\" is typically the position along an individual Bezier path (time) and it's NOT linear, meaning it could accelerate/decelerate based on the control points whereas the \"p\" or \"progress\" value is linearly mapped to the whole path, so it shouldn't really accelerate/decelerate based on control points. So a progress of 0.2 would be almost exactly 20% along the path. \"t\" is ONLY in an individual Bezier piece.\n */\n\n//accepts basic selector text, a path instance, a RawPath instance, or a Segment and returns a RawPath (makes it easy to homogenize things). If an element or selector text is passed in, it'll also cache the value so that if it's queried again, it'll just take the path data from there instead of parsing it all over again (as long as the path data itself hasn't changed - it'll check).\nexport function getRawPath(value) {\n\tvalue = (_isString(value) && _selectorExp.test(value)) ? document.querySelector(value) || value : value;\n\tlet e = value.getAttribute ? value : 0,\n\t\trawPath;\n\tif (e && (value = value.getAttribute(\"d\"))) {\n\t\t//implements caching\n\t\tif (!e._gsPath) {\n\t\t\te._gsPath = {};\n\t\t}\n\t\trawPath = e._gsPath[value];\n\t\treturn (rawPath && !rawPath._dirty) ? rawPath : (e._gsPath[value] = stringToRawPath(value));\n\t}\n\treturn !value ? console.warn(\"Expecting a <path> element or an SVG path data string\") : _isString(value) ? stringToRawPath(value) : (_isNumber(value[0])) ? [value] : value;\n}\n\n//copies a RawPath WITHOUT the length meta data (for speed)\nexport function copyRawPath(rawPath) {\n\tlet a = [],\n\t\ti = 0;\n\tfor (; i < rawPath.length; i++) {\n\t\ta[i] = _copyMetaData(rawPath[i], rawPath[i].slice(0));\n\t}\n\treturn _copyMetaData(rawPath, a);\n}\n\nexport function reverseSegment(segment) {\n\tlet i = 0,\n\t\ty;\n\tsegment.reverse(); //this will invert the order y, x, y, x so we must flip it back.\n\tfor (; i < segment.length; i += 2) {\n\t\ty = segment[i];\n\t\tsegment[i] = segment[i+1];\n\t\tsegment[i+1] = y;\n\t}\n\tsegment.reversed = !segment.reversed;\n}\n\n\n\nlet _createPath = (e, ignore) => {\n\t\tlet path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\"),\n\t\t\tattr = [].slice.call(e.attributes),\n\t\t\ti = attr.length,\n\t\t\tname;\n\t\tignore = \",\" + ignore + \",\";\n\t\twhile (--i > -1) {\n\t\t\tname = attr[i].nodeName.toLowerCase(); //in Microsoft Edge, if you don't set the attribute with a lowercase name, it doesn't render correctly! Super weird.\n\t\t\tif (ignore.indexOf(\",\" + name + \",\") < 0) {\n\t\t\t\tpath.setAttributeNS(null, name, attr[i].nodeValue);\n\t\t\t}\n\t\t}\n\t\treturn path;\n\t},\n\t_typeAttrs = {\n\t\trect:\"rx,ry,x,y,width,height\",\n\t\tcircle:\"r,cx,cy\",\n\t\tellipse:\"rx,ry,cx,cy\",\n\t\tline:\"x1,x2,y1,y2\"\n\t},\n\t_attrToObj = (e, attrs) => {\n\t\tlet props = attrs ? attrs.split(\",\") : [],\n\t\t\tobj = {},\n\t\t\ti = props.length;\n\t\twhile (--i > -1) {\n\t\t\tobj[props[i]] = +e.getAttribute(props[i]) || 0;\n\t\t}\n\t\treturn obj;\n\t};\n\n//converts an SVG shape like <circle>, <rect>, <polygon>, <polyline>, <ellipse>, etc. to a <path>, swapping it in and copying the attributes to match.\nexport function convertToPath(element, swap) {\n\tlet type = element.tagName.toLowerCase(),\n\t\tcirc = 0.552284749831,\n\t\tdata, x, y, r, ry, path, rcirc, rycirc, points, w, h, x2, x3, x4, x5, x6, y2, y3, y4, y5, y6, attr;\n\tif (type === \"path\" || !element.getBBox) {\n\t\treturn element;\n\t}\n\tpath = _createPath(element, \"x,y,width,height,cx,cy,rx,ry,r,x1,x2,y1,y2,points\");\n\tattr = _attrToObj(element, _typeAttrs[type]);\n\tif (type === \"rect\") {\n\t\tr = attr.rx;\n\t\try = attr.ry || r;\n\t\tx = attr.x;\n\t\ty = attr.y;\n\t\tw = attr.width - r * 2;\n\t\th = attr.height - ry * 2;\n\t\tif (r || ry) { //if there are rounded corners, render cubic beziers\n\t\t\tx2 = x + r * (1 - circ);\n\t\t\tx3 = x + r;\n\t\t\tx4 = x3 + w;\n\t\t\tx5 = x4 + r * circ;\n\t\t\tx6 = x4 + r;\n\t\t\ty2 = y + ry * (1 - circ);\n\t\t\ty3 = y + ry;\n\t\t\ty4 = y3 + h;\n\t\t\ty5 = y4 + ry * circ;\n\t\t\ty6 = y4 + ry;\n\t\t\tdata = \"M\" + x6 + \",\" + y3 + \" V\" + y4 + \" C\" + [x6, y5, x5, y6, x4, y6, x4 - (x4 - x3) / 3, y6, x3 + (x4 - x3) / 3, y6, x3, y6, x2, y6, x, y5, x, y4, x, y4 - (y4 - y3) / 3, x, y3 + (y4 - y3) / 3, x, y3, x, y2, x2, y, x3, y, x3 + (x4 - x3) / 3, y, x4 - (x4 - x3) / 3, y, x4, y, x5, y, x6, y2, x6, y3].join(\",\") + \"z\";\n\t\t} else {\n\t\t\tdata = \"M\" + (x + w) + \",\" + y + \" v\" + h + \" h\" + (-w) + \" v\" + (-h) + \" h\" + w + \"z\";\n\t\t}\n\n\t} else if (type === \"circle\" || type === \"ellipse\") {\n\t\tif (type === \"circle\") {\n\t\t\tr = ry = attr.r;\n\t\t\trycirc = r * circ;\n\t\t} else {\n\t\t\tr = attr.rx;\n\t\t\try = attr.ry;\n\t\t\trycirc = ry * circ;\n\t\t}\n\t\tx = attr.cx;\n\t\ty = attr.cy;\n\t\trcirc = r * circ;\n\t\tdata = \"M\" + (x+r) + \",\" + y + \" C\" + [x+r, y + rycirc, x + rcirc, y + ry, x, y + ry, x - rcirc, y + ry, x - r, y + rycirc, x - r, y, x - r, y - rycirc, x - rcirc, y - ry, x, y - ry, x + rcirc, y - ry, x + r, y - rycirc, x + r, y].join(\",\") + \"z\";\n\t} else if (type === \"line\") {\n\t\tdata = \"M\" + attr.x1 + \",\" + attr.y1 + \" L\" + attr.x2 + \",\" + attr.y2; //previously, we just converted to \"Mx,y Lx,y\" but Safari has bugs that cause that not to render properly when using a stroke-dasharray that's not fully visible! Using a cubic bezier fixes that issue.\n\t} else if (type === \"polyline\" || type === \"polygon\") {\n\t\tpoints = (element.getAttribute(\"points\") + \"\").match(_numbersExp) || [];\n\t\tx = points.shift();\n\t\ty = points.shift();\n\t\tdata = \"M\" + x + \",\" + y + \" L\" + points.join(\",\");\n\t\tif (type === \"polygon\") {\n\t\t\tdata += \",\" + x + \",\" + y + \"z\";\n\t\t}\n\t}\n\tpath.setAttribute(\"d\", rawPathToString(path._gsRawPath = stringToRawPath(data)));\n\tif (swap && element.parentNode) {\n\t\telement.parentNode.insertBefore(path, element);\n\t\telement.parentNode.removeChild(element);\n\t}\n\treturn path;\n}\n\n\n\n//returns the rotation (in degrees) at a particular progress on a rawPath (the slope of the tangent)\nexport function getRotationAtProgress(rawPath, progress) {\n\tlet d = getProgressData(rawPath, progress >= 1 ? 1 - 1e-9 : progress ? progress : 1e-9);\n\treturn getRotationAtBezierT(d.segment, d.i, d.t);\n}\n\nfunction getRotationAtBezierT(segment, i, t) {\n\tlet a = segment[i],\n\t\tb = segment[i+2],\n\t\tc = segment[i+4],\n\t\tx;\n\ta += (b - a) * t;\n\tb += (c - b) * t;\n\ta += (b - a) * t;\n\tx = b + ((c + (segment[i+6] - c) * t) - b) * t - a;\n\ta = segment[i+1];\n\tb = segment[i+3];\n\tc = segment[i+5];\n\ta += (b - a) * t;\n\tb += (c - b) * t;\n\ta += (b - a) * t;\n\treturn _round(_atan2(b + ((c + (segment[i+7] - c) * t) - b) * t - a, x) * _RAD2DEG);\n}\n\nexport function sliceRawPath(rawPath, start, end) {\n\tend = _isUndefined(end) ? 1 : _roundPrecise(end) || 0; // we must round to avoid issues like 4.15 / 8 = 0.8300000000000001 instead of 0.83 or 2.8 / 5 = 0.5599999999999999 instead of 0.56 and if someone is doing a loop like start: 2.8 / 0.5, end: 2.8 / 0.5 + 1.\n\tstart = _roundPrecise(start) || 0;\n\tlet loops = Math.max(0, ~~(_abs(end - start) - 1e-8)),\n\t\tpath = copyRawPath(rawPath);\n\tif (start > end) {\n\t\tstart = 1 - start;\n\t\tend = 1 - end;\n\t\t_reverseRawPath(path);\n\t\tpath.totalLength = 0;\n\t}\n\tif (start < 0 || end < 0) {\n\t\tlet offset = Math.abs(~~Math.min(start, end)) + 1;\n\t\tstart += offset;\n\t\tend += offset;\n\t}\n\tpath.totalLength || cacheRawPathMeasurements(path);\n\tlet wrap = (end > 1),\n\t\ts = getProgressData(path, start, _temp, true),\n\t\te = getProgressData(path, end, _temp2),\n\t\teSeg = e.segment,\n\t\tsSeg = s.segment,\n\t\teSegIndex = e.segIndex,\n\t\tsSegIndex = s.segIndex,\n\t\tei = e.i,\n\t\tsi = s.i,\n\t\tsameSegment = (sSegIndex === eSegIndex),\n\t\tsameBezier = (ei === si && sameSegment),\n\t\twrapsBehind, sShift, eShift, i, copy, totalSegments, l, j;\n\tif (wrap || loops) {\n\t\twrapsBehind = eSegIndex < sSegIndex || (sameSegment && ei < si) || (sameBezier && e.t < s.t);\n\t\tif (_splitSegment(path, sSegIndex, si, s.t)) {\n\t\t\tsSegIndex++;\n\t\t\tif (!wrapsBehind) {\n\t\t\t\teSegIndex++;\n\t\t\t\tif (sameBezier) {\n\t\t\t\t\te.t = (e.t - s.t) / (1 - s.t);\n\t\t\t\t\tei = 0;\n\t\t\t\t} else if (sameSegment) {\n\t\t\t\t\tei -= si;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (Math.abs(1 - (end - start)) < 1e-5) {\n\t\t\teSegIndex = sSegIndex - 1;\n\t\t} else if (!e.t && eSegIndex) {\n\t\t\teSegIndex--;\n\t\t} else if (_splitSegment(path, eSegIndex, ei, e.t) && wrapsBehind) {\n\t\t\tsSegIndex++;\n\t\t}\n\t\tif (s.t === 1) {\n\t\t\tsSegIndex = (sSegIndex + 1) % path.length;\n\t\t}\n\t\tcopy = [];\n\t\ttotalSegments = path.length;\n\t\tl = 1 + totalSegments * loops;\n\t\tj = sSegIndex;\n\t\tl += ((totalSegments - sSegIndex) + eSegIndex) % totalSegments;\n\t\tfor (i = 0; i < l; i++) {\n\t\t\t_appendOrMerge(copy, path[j++ % totalSegments]);\n\t\t}\n\t\tpath = copy;\n\t} else {\n\t\teShift = e.t === 1 ? 6 : subdivideSegment(eSeg, ei, e.t);\n\t\tif (start !== end) {\n\t\t\tsShift = subdivideSegment(sSeg, si, sameBezier ? s.t / e.t : s.t);\n\t\t\tsameSegment && (eShift += sShift);\n\t\t\teSeg.splice(ei + eShift + 2);\n\t\t\t(sShift || si) && sSeg.splice(0, si + sShift);\n\t\t\ti = path.length;\n\t\t\twhile (i--) {\n\t\t\t\t//chop off any extra segments\n\t\t\t\t(i < sSegIndex || i > eSegIndex) &&\tpath.splice(i, 1);\n\t\t\t}\n\t\t} else {\n\t\t\teSeg.angle = getRotationAtBezierT(eSeg, ei + eShift, 0); //record the value before we chop because it'll be impossible to determine the angle after its length is 0!\n\t\t\tei += eShift;\n\t\t\ts = eSeg[ei];\n\t\t\te = eSeg[ei+1];\n\t\t\teSeg.length = eSeg.totalLength = 0;\n\t\t\teSeg.totalPoints = path.totalPoints = 8;\n\t\t\teSeg.push(s, e, s, e, s, e, s, e);\n\t\t}\n\t}\n\tpath.totalLength = 0;\n\treturn path;\n}\n\n//measures a Segment according to its resolution (so if segment.resolution is 6, for example, it'll take 6 samples equally across each Bezier) and create/populate a \"samples\" Array that has the length up to each of those sample points (always increasing from the start) as well as a \"lookup\" array that's broken up according to the smallest distance between 2 samples. This gives us a very fast way of looking up a progress position rather than looping through all the points/Beziers. You can optionally have it only measure a subset, starting at startIndex and going for a specific number of beziers (remember, there are 3 x/y pairs each, for a total of 6 elements for each Bezier). It will also populate a \"totalLength\" property, but that's not generally super accurate because by default it'll only take 6 samples per Bezier. But for performance reasons, it's perfectly adequate for measuring progress values along the path. If you need a more accurate totalLength, either increase the resolution or use the more advanced bezierToPoints() method which keeps adding points until they don't deviate by more than a certain precision value.\nfunction measureSegment(segment, startIndex, bezierQty) {\n\tstartIndex = startIndex || 0;\n\tif (!segment.samples) {\n\t\tsegment.samples = [];\n\t\tsegment.lookup = [];\n\t}\n\tlet resolution = ~~segment.resolution || 12,\n\t\tinc = 1 / resolution,\n\t\tendIndex = bezierQty ? startIndex + bezierQty * 6 + 1 : segment.length,\n\t\tx1 = segment[startIndex],\n\t\ty1 = segment[startIndex + 1],\n\t\tsamplesIndex = startIndex ? (startIndex / 6) * resolution : 0,\n\t\tsamples = segment.samples,\n\t\tlookup = segment.lookup,\n\t\tmin = (startIndex ? segment.minLength : _largeNum) || _largeNum,\n\t\tprevLength = samples[samplesIndex + bezierQty * resolution - 1],\n\t\tlength = startIndex ? samples[samplesIndex-1] : 0,\n\t\ti, j, x4, x3, x2, xd, xd1, y4, y3, y2, yd, yd1, inv, t, lengthIndex, l, segLength;\n\tsamples.length = lookup.length = 0;\n\tfor (j = startIndex + 2; j < endIndex; j += 6) {\n\t\tx4 = segment[j + 4] - x1;\n\t\tx3 = segment[j + 2] - x1;\n\t\tx2 = segment[j] - x1;\n\t\ty4 = segment[j + 5] - y1;\n\t\ty3 = segment[j + 3] - y1;\n\t\ty2 = segment[j + 1] - y1;\n\t\txd = xd1 = yd = yd1 = 0;\n\t\tif (_abs(x4) < .01 && _abs(y4) < .01 && _abs(x2) + _abs(y2) < .01) { //dump points that are sufficiently close (basically right on top of each other, making a bezier super tiny or 0 length)\n\t\t\tif (segment.length > 8) {\n\t\t\t\tsegment.splice(j, 6);\n\t\t\t\tj -= 6;\n\t\t\t\tendIndex -= 6;\n\t\t\t}\n\t\t} else {\n\t\t\tfor (i = 1; i <= resolution; i++) {\n\t\t\t\tt = inc * i;\n\t\t\t\tinv = 1 - t;\n\t\t\t\txd = xd1 - (xd1 = (t * t * x4 + 3 * inv * (t * x3 + inv * x2)) * t);\n\t\t\t\tyd = yd1 - (yd1 = (t * t * y4 + 3 * inv * (t * y3 + inv * y2)) * t);\n\t\t\t\tl = _sqrt(yd * yd + xd * xd);\n\t\t\t\tif (l < min) {\n\t\t\t\t\tmin = l;\n\t\t\t\t}\n\t\t\t\tlength += l;\n\t\t\t\tsamples[samplesIndex++] = length;\n\t\t\t}\n\t\t}\n\t\tx1 += x4;\n\t\ty1 += y4;\n\t}\n\tif (prevLength) {\n\t\tprevLength -= length;\n\t\tfor (; samplesIndex < samples.length; samplesIndex++) {\n\t\t\tsamples[samplesIndex] += prevLength;\n\t\t}\n\t}\n\tif (samples.length && min) {\n\t\tsegment.totalLength = segLength = samples[samples.length-1] || 0;\n\t\tsegment.minLength = min;\n\t\tif (segLength / min < 9999) { // if the lookup would require too many values (memory problem), we skip this and instead we use a loop to lookup values directly in the samples Array\n\t\t\tl = lengthIndex = 0;\n\t\t\tfor (i = 0; i < segLength; i += min) {\n\t\t\t\tlookup[l++] = (samples[lengthIndex] < i) ? ++lengthIndex : lengthIndex;\n\t\t\t}\n\t\t}\n\t} else {\n\t\tsegment.totalLength = samples[0] = 0;\n\t}\n\treturn startIndex ? length - samples[startIndex / 2 - 1] : length;\n}\n\nexport function cacheRawPathMeasurements(rawPath, resolution) {\n\tlet pathLength, points, i;\n\tfor (i = pathLength = points = 0; i < rawPath.length; i++) {\n\t\trawPath[i].resolution = ~~resolution || 12; //steps per Bezier curve (anchor, 2 control points, to anchor)\n\t\tpoints += rawPath[i].length;\n\t\tpathLength += measureSegment(rawPath[i]);\n\t}\n\trawPath.totalPoints = points;\n\trawPath.totalLength = pathLength;\n\treturn rawPath;\n}\n\n//divide segment[i] at position t (value between 0 and 1, progress along that particular cubic bezier segment that starts at segment[i]). Returns how many elements were spliced into the segment array (either 0 or 6)\nexport function subdivideSegment(segment, i, t) {\n\tif (t <= 0 || t >= 1) {\n\t\treturn 0;\n\t}\n\tlet ax = segment[i],\n\t\tay = segment[i+1],\n\t\tcp1x = segment[i+2],\n\t\tcp1y = segment[i+3],\n\t\tcp2x = segment[i+4],\n\t\tcp2y = segment[i+5],\n\t\tbx = segment[i+6],\n\t\tby = segment[i+7],\n\t\tx1a = ax + (cp1x - ax) * t,\n\t\tx2 = cp1x + (cp2x - cp1x) * t,\n\t\ty1a = ay + (cp1y - ay) * t,\n\t\ty2 = cp1y + (cp2y - cp1y) * t,\n\t\tx1 = x1a + (x2 - x1a) * t,\n\t\ty1 = y1a + (y2 - y1a) * t,\n\t\tx2a = cp2x + (bx - cp2x) * t,\n\t\ty2a = cp2y + (by - cp2y) * t;\n\tx2 += (x2a - x2) * t;\n\ty2 += (y2a - y2) * t;\n\tsegment.splice(i + 2, 4,\n\t\t_round(x1a),                  //first control point\n\t\t_round(y1a),\n\t\t_round(x1),                   //second control point\n\t\t_round(y1),\n\t\t_round(x1 + (x2 - x1) * t),   //new fabricated anchor on line\n\t\t_round(y1 + (y2 - y1) * t),\n\t\t_round(x2),                   //third control point\n\t\t_round(y2),\n\t\t_round(x2a),                  //fourth control point\n\t\t_round(y2a)\n\t);\n\tsegment.samples && segment.samples.splice(((i / 6) * segment.resolution) | 0, 0, 0, 0, 0, 0, 0, 0);\n\treturn 6;\n}\n\n// returns an object {path, segment, segIndex, i, t}\nfunction getProgressData(rawPath, progress, decoratee, pushToNextIfAtEnd) {\n\tdecoratee = decoratee || {};\n\trawPath.totalLength || cacheRawPathMeasurements(rawPath);\n\tif (progress < 0 || progress > 1) {\n\t\tprogress = _wrapProgress(progress);\n\t}\n\tlet segIndex = 0,\n\t\tsegment = rawPath[0],\n\t\tsamples, resolution, length, min, max, i, t;\n\tif (!progress) {\n\t\tt = i = segIndex = 0;\n\t\tsegment = rawPath[0];\n\t} else if (progress === 1) {\n\t\tt = 1;\n\t\tsegIndex = rawPath.length - 1;\n\t\tsegment = rawPath[segIndex];\n\t\ti = segment.length - 8;\n\t} else {\n\t\tif (rawPath.length > 1) { //speed optimization: most of the time, there's only one segment so skip the recursion.\n\t\t\tlength = rawPath.totalLength * progress;\n\t\t\tmax = i = 0;\n\t\t\twhile ((max += rawPath[i++].totalLength) < length) {\n\t\t\t\tsegIndex = i;\n\t\t\t}\n\t\t\tsegment = rawPath[segIndex];\n\t\t\tmin = max - segment.totalLength;\n\t\t\tprogress = ((length - min) / (max - min)) || 0;\n\t\t}\n\t\tsamples = segment.samples;\n\t\tresolution = segment.resolution; //how many samples per cubic bezier chunk\n\t\tlength = segment.totalLength * progress;\n\t\ti = segment.lookup.length ? segment.lookup[~~(length / segment.minLength)] || 0 : _getSampleIndex(samples, length, progress);\n\t\tmin = i ? samples[i-1] : 0;\n\t\tmax = samples[i];\n\t\tif (max < length) {\n\t\t\tmin = max;\n\t\t\tmax = samples[++i];\n\t\t}\n\t\tt = (1 / resolution) * (((length - min) / (max - min)) + ((i % resolution)));\n\t\ti = ~~(i / resolution) * 6;\n\t\tif (pushToNextIfAtEnd && t === 1) {\n\t\t\tif (i + 6 < segment.length) {\n\t\t\t\ti += 6;\n\t\t\t\tt = 0;\n\t\t\t} else if (segIndex + 1 < rawPath.length) {\n\t\t\t\ti = t = 0;\n\t\t\t\tsegment = rawPath[++segIndex];\n\t\t\t}\n\t\t}\n\t}\n\tdecoratee.t = t;\n\tdecoratee.i = i;\n\tdecoratee.path = rawPath;\n\tdecoratee.segment = segment;\n\tdecoratee.segIndex = segIndex;\n\treturn decoratee;\n}\n\nexport function getPositionOnPath(rawPath, progress, includeAngle, point) {\n\tlet segment = rawPath[0],\n\t\tresult = point || {},\n\t\tsamples, resolution, length, min, max, i, t, a, inv;\n\tif (progress < 0 || progress > 1) {\n\t\tprogress = _wrapProgress(progress);\n\t}\n\tsegment.lookup || cacheRawPathMeasurements(rawPath);\n\tif (rawPath.length > 1) { //speed optimization: most of the time, there's only one segment so skip the recursion.\n\t\tlength = rawPath.totalLength * progress;\n\t\tmax = i = 0;\n\t\twhile ((max += rawPath[i++].totalLength) < length) {\n\t\t\tsegment = rawPath[i];\n\t\t}\n\t\tmin = max - segment.totalLength;\n\t\tprogress = ((length - min) / (max - min)) || 0;\n\t}\n\tsamples = segment.samples;\n\tresolution = segment.resolution;\n\tlength = segment.totalLength * progress;\n\ti = segment.lookup.length ? segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0 : _getSampleIndex(samples, length, progress);\n\tmin = i ? samples[i-1] : 0;\n\tmax = samples[i];\n\tif (max < length) {\n\t\tmin = max;\n\t\tmax = samples[++i];\n\t}\n\tt = ((1 / resolution) * (((length - min) / (max - min)) + ((i % resolution)))) || 0;\n\tinv = 1 - t;\n\ti = ~~(i / resolution) * 6;\n\ta = segment[i];\n\tresult.x = _round((t * t * (segment[i + 6] - a) + 3 * inv * (t * (segment[i + 4] - a) + inv * (segment[i + 2] - a))) * t + a);\n\tresult.y = _round((t * t * (segment[i + 7] - (a = segment[i+1])) + 3 * inv * (t * (segment[i + 5] - a) + inv * (segment[i + 3] - a))) * t + a);\n\tif (includeAngle) {\n\t\tresult.angle = segment.totalLength ? getRotationAtBezierT(segment, i, t >= 1 ? 1 - 1e-9 : t ? t : 1e-9) : segment.angle || 0;\n\t}\n\treturn result;\n}\n\n\n\n//applies a matrix transform to RawPath (or a segment in a RawPath) and returns whatever was passed in (it transforms the values in the array(s), not a copy).\nexport function transformRawPath(rawPath, a, b, c, d, tx, ty) {\n\tlet j = rawPath.length,\n\t\tsegment, l, i, x, y;\n\twhile (--j > -1) {\n\t\tsegment = rawPath[j];\n\t\tl = segment.length;\n\t\tfor (i = 0; i < l; i += 2) {\n\t\t\tx = segment[i];\n\t\t\ty = segment[i+1];\n\t\t\tsegment[i] = x * a + y * c + tx;\n\t\t\tsegment[i+1] = x * b + y * d + ty;\n\t\t}\n\t}\n\trawPath._dirty = 1;\n\treturn rawPath;\n}\n\n\n\n// translates SVG arc data into a segment (cubic beziers). Angle is in degrees.\nfunction arcToSegment(lastX, lastY, rx, ry, angle, largeArcFlag, sweepFlag, x, y) {\n\tif (lastX === x && lastY === y) {\n\t\treturn;\n\t}\n\trx = _abs(rx);\n\try = _abs(ry);\n\tlet angleRad = (angle % 360) * _DEG2RAD,\n\t\tcosAngle = _cos(angleRad),\n\t\tsinAngle = _sin(angleRad),\n\t\tPI = Math.PI,\n\t\tTWOPI = PI * 2,\n\t\tdx2 = (lastX - x) / 2,\n\t\tdy2 = (lastY - y) / 2,\n\t\tx1 = (cosAngle * dx2 + sinAngle * dy2),\n\t\ty1 = (-sinAngle * dx2 + cosAngle * dy2),\n\t\tx1_sq = x1 * x1,\n\t\ty1_sq = y1 * y1,\n\t\tradiiCheck = x1_sq / (rx * rx) + y1_sq / (ry * ry);\n\tif (radiiCheck > 1) {\n\t\trx = _sqrt(radiiCheck) * rx;\n\t\try = _sqrt(radiiCheck) * ry;\n\t}\n\tlet rx_sq = rx * rx,\n\t\try_sq = ry * ry,\n\t\tsq = ((rx_sq * ry_sq) - (rx_sq * y1_sq) - (ry_sq * x1_sq)) / ((rx_sq * y1_sq) + (ry_sq * x1_sq));\n\tif (sq < 0) {\n\t\tsq = 0;\n\t}\n\tlet coef = ((largeArcFlag === sweepFlag) ? -1 : 1) * _sqrt(sq),\n\t\tcx1 = coef * ((rx * y1) / ry),\n\t\tcy1 = coef * -((ry * x1) / rx),\n\t\tsx2 = (lastX + x) / 2,\n\t\tsy2 = (lastY + y) / 2,\n\t\tcx = sx2 + (cosAngle * cx1 - sinAngle * cy1),\n\t\tcy = sy2 + (sinAngle * cx1 + cosAngle * cy1),\n\t\tux = (x1 - cx1) / rx,\n\t\tuy = (y1 - cy1) / ry,\n\t\tvx = (-x1 - cx1) / rx,\n\t\tvy = (-y1 - cy1) / ry,\n\t\ttemp = ux * ux + uy * uy,\n\t\tangleStart = ((uy < 0) ? -1 : 1) * Math.acos(ux / _sqrt(temp)),\n\t\tangleExtent = ((ux * vy - uy * vx < 0) ? -1 : 1) * Math.acos((ux * vx + uy * vy) / _sqrt(temp * (vx * vx + vy * vy)));\n\tisNaN(angleExtent) && (angleExtent = PI); //rare edge case. Math.cos(-1) is NaN.\n\tif (!sweepFlag && angleExtent > 0) {\n\t\tangleExtent -= TWOPI;\n\t} else if (sweepFlag && angleExtent < 0) {\n\t\tangleExtent += TWOPI;\n\t}\n\tangleStart %= TWOPI;\n\tangleExtent %= TWOPI;\n\tlet segments = Math.ceil(_abs(angleExtent) / (TWOPI / 4)),\n\t\trawPath = [],\n\t\tangleIncrement = angleExtent / segments,\n\t\tcontrolLength = 4 / 3 * _sin(angleIncrement / 2) / (1 + _cos(angleIncrement / 2)),\n\t\tma = cosAngle * rx,\n\t\tmb = sinAngle * rx,\n\t\tmc = sinAngle * -ry,\n\t\tmd = cosAngle * ry,\n\t\ti;\n\tfor (i = 0; i < segments; i++) {\n\t\tangle = angleStart + i * angleIncrement;\n\t\tx1 = _cos(angle);\n\t\ty1 = _sin(angle);\n\t\tux = _cos(angle += angleIncrement);\n\t\tuy = _sin(angle);\n\t\trawPath.push(x1 - controlLength * y1, y1 + controlLength * x1, ux + controlLength * uy, uy - controlLength * ux, ux, uy);\n\t}\n\t//now transform according to the actual size of the ellipse/arc (the beziers were noramlized, between 0 and 1 on a circle).\n\tfor (i = 0; i < rawPath.length; i+=2) {\n\t\tx1 = rawPath[i];\n\t\ty1 = rawPath[i+1];\n\t\trawPath[i] = x1 * ma + y1 * mc + cx;\n\t\trawPath[i+1] = x1 * mb + y1 * md + cy;\n\t}\n\trawPath[i-2] = x; //always set the end to exactly where it's supposed to be\n\trawPath[i-1] = y;\n\treturn rawPath;\n}\n\n//Spits back a RawPath with absolute coordinates. Each segment starts with a \"moveTo\" command (x coordinate, then y) and then 2 control points (x, y, x, y), then anchor. The goal is to minimize memory and maximize speed.\nexport function stringToRawPath(d) {\n\tlet a = (d + \"\").replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp) || [], //some authoring programs spit out very small numbers in scientific notation like \"1e-5\", so make sure we round that down to 0 first.\n\t\tpath = [],\n\t\trelativeX = 0,\n\t\trelativeY = 0,\n\t\ttwoThirds = 2 / 3,\n\t\telements = a.length,\n\t\tpoints = 0,\n\t\terrorMessage = \"ERROR: malformed path: \" + d,\n\t\ti, j, x, y, command, isRelative, segment, startX, startY, difX, difY, beziers, prevCommand, flag1, flag2,\n\t\tline = function(sx, sy, ex, ey) {\n\t\t\tdifX = (ex - sx) / 3;\n\t\t\tdifY = (ey - sy) / 3;\n\t\t\tsegment.push(sx + difX, sy + difY, ex - difX, ey - difY, ex, ey);\n\t\t};\n\tif (!d || !isNaN(a[0]) || isNaN(a[1])) {\n\t\tconsole.log(errorMessage);\n\t\treturn path;\n\t}\n\tfor (i = 0; i < elements; i++) {\n\t\tprevCommand = command;\n\t\tif (isNaN(a[i])) {\n\t\t\tcommand = a[i].toUpperCase();\n\t\t\tisRelative = (command !== a[i]); //lower case means relative\n\t\t} else { //commands like \"C\" can be strung together without any new command characters between.\n\t\t\ti--;\n\t\t}\n\t\tx = +a[i + 1];\n\t\ty = +a[i + 2];\n\t\tif (isRelative) {\n\t\t\tx += relativeX;\n\t\t\ty += relativeY;\n\t\t}\n\t\tif (!i) {\n\t\t\tstartX = x;\n\t\t\tstartY = y;\n\t\t}\n\n\t\t// \"M\" (move)\n\t\tif (command === \"M\") {\n\t\t\tif (segment) {\n\t\t\t\tif (segment.length < 8) { //if the path data was funky and just had a M with no actual drawing anywhere, skip it.\n\t\t\t\t\tpath.length -= 1;\n\t\t\t\t} else {\n\t\t\t\t\tpoints += segment.length;\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = startX = x;\n\t\t\trelativeY = startY = y;\n\t\t\tsegment = [x, y];\n\t\t\tpath.push(segment);\n\t\t\ti += 2;\n\t\t\tcommand = \"L\"; //an \"M\" with more than 2 values gets interpreted as \"lineTo\" commands (\"L\").\n\n\t\t// \"C\" (cubic bezier)\n\t\t} else if (command === \"C\") {\n\t\t\tif (!segment) {\n\t\t\t\tsegment = [0, 0];\n\t\t\t}\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\t//note: \"*1\" is just a fast/short way to cast the value as a Number. WAAAY faster in Chrome, slightly slower in Firefox.\n\t\t\tsegment.push(x,\ty, relativeX + a[i + 3] * 1, relativeY + a[i + 4] * 1, (relativeX += a[i + 5] * 1),\t(relativeY += a[i + 6] * 1));\n\t\t\ti += 6;\n\n\t\t// \"S\" (continuation of cubic bezier)\n\t\t} else if (command === \"S\") {\n\t\t\tdifX = relativeX;\n\t\t\tdifY = relativeY;\n\t\t\tif (prevCommand === \"C\" || prevCommand === \"S\") {\n\t\t\t\tdifX += relativeX - segment[segment.length - 4];\n\t\t\t\tdifY += relativeY - segment[segment.length - 3];\n\t\t\t}\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\tsegment.push(difX, difY, x,\ty, (relativeX += a[i + 3] * 1), (relativeY += a[i + 4] * 1));\n\t\t\ti += 4;\n\n\t\t// \"Q\" (quadratic bezier)\n\t\t} else if (command === \"Q\") {\n\t\t\tdifX = relativeX + (x - relativeX) * twoThirds;\n\t\t\tdifY = relativeY + (y - relativeY) * twoThirds;\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\trelativeX += a[i + 3] * 1;\n\t\t\trelativeY += a[i + 4] * 1;\n\t\t\tsegment.push(difX, difY, relativeX + (x - relativeX) * twoThirds, relativeY + (y - relativeY) * twoThirds, relativeX, relativeY);\n\t\t\ti += 4;\n\n\t\t// \"T\" (continuation of quadratic bezier)\n\t\t} else if (command === \"T\") {\n\t\t\tdifX = relativeX - segment[segment.length - 4];\n\t\t\tdifY = relativeY - segment[segment.length - 3];\n\t\t\tsegment.push(relativeX + difX, relativeY + difY, x + ((relativeX + difX * 1.5) - x) * twoThirds, y + ((relativeY + difY * 1.5) - y) * twoThirds, (relativeX = x), (relativeY = y));\n\t\t\ti += 2;\n\n\t\t// \"H\" (horizontal line)\n\t\t} else if (command === \"H\") {\n\t\t\tline(relativeX, relativeY, (relativeX = x), relativeY);\n\t\t\ti += 1;\n\n\t\t// \"V\" (vertical line)\n\t\t} else if (command === \"V\") {\n\t\t\t//adjust values because the first (and only one) isn't x in this case, it's y.\n\t\t\tline(relativeX, relativeY, relativeX, (relativeY = x + (isRelative ? relativeY - relativeX : 0)));\n\t\t\ti += 1;\n\n\t\t// \"L\" (line) or \"Z\" (close)\n\t\t} else if (command === \"L\" || command === \"Z\") {\n\t\t\tif (command === \"Z\") {\n\t\t\t\tx = startX;\n\t\t\t\ty = startY;\n\t\t\t\tsegment.closed = true;\n\t\t\t}\n\t\t\tif (command === \"L\" || _abs(relativeX - x) > 0.5 || _abs(relativeY - y) > 0.5) {\n\t\t\t\tline(relativeX, relativeY, x, y);\n\t\t\t\tif (command === \"L\") {\n\t\t\t\t\ti += 2;\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = x;\n\t\t\trelativeY = y;\n\n\t\t// \"A\" (arc)\n\t\t} else if (command === \"A\") {\n\t\t\tflag1 = a[i+4];\n\t\t\tflag2 = a[i+5];\n\t\t\tdifX = a[i+6];\n\t\t\tdifY = a[i+7];\n\t\t\tj = 7;\n\t\t\tif (flag1.length > 1) { // for cases when the flags are merged, like \"a8 8 0 018 8\" (the 0 and 1 flags are WITH the x value of 8, but it could also be \"a8 8 0 01-8 8\" so it may include x or not)\n\t\t\t\tif (flag1.length < 3) {\n\t\t\t\t\tdifY = difX;\n\t\t\t\t\tdifX = flag2;\n\t\t\t\t\tj--;\n\t\t\t\t} else {\n\t\t\t\t\tdifY = flag2;\n\t\t\t\t\tdifX = flag1.substr(2);\n\t\t\t\t\tj-=2;\n\t\t\t\t}\n\t\t\t\tflag2 = flag1.charAt(1);\n\t\t\t\tflag1 = flag1.charAt(0);\n\t\t\t}\n\t\t\tbeziers = arcToSegment(relativeX, relativeY, +a[i+1], +a[i+2], +a[i+3], +flag1, +flag2, (isRelative ? relativeX : 0) + difX*1, (isRelative ? relativeY : 0) + difY*1);\n\t\t\ti += j;\n\t\t\tif (beziers) {\n\t\t\t\tfor (j = 0; j < beziers.length; j++) {\n\t\t\t\t\tsegment.push(beziers[j]);\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = segment[segment.length-2];\n\t\t\trelativeY = segment[segment.length-1];\n\n\t\t} else {\n\t\t\tconsole.log(errorMessage);\n\t\t}\n\t}\n\ti = segment.length;\n\tif (i < 6) { //in case there's odd SVG like a M0,0 command at the very end.\n\t\tpath.pop();\n\t\ti = 0;\n\t} else if (segment[0] === segment[i-2] && segment[1] === segment[i-1]) {\n\t\tsegment.closed = true;\n\t}\n\tpath.totalPoints = points + i;\n\treturn path;\n}\n\n//populates the points array in alternating x/y values (like [x, y, x, y...] instead of individual point objects [{x, y}, {x, y}...] to conserve memory and stay in line with how we're handling segment arrays\nexport function bezierToPoints(x1, y1, x2, y2, x3, y3, x4, y4, threshold, points, index) {\n\tlet x12 = (x1 + x2) / 2,\n\t\ty12 = (y1 + y2) / 2,\n\t\tx23 = (x2 + x3) / 2,\n\t\ty23 = (y2 + y3) / 2,\n\t\tx34 = (x3 + x4) / 2,\n\t\ty34 = (y3 + y4) / 2,\n\t\tx123 = (x12 + x23) / 2,\n\t\ty123 = (y12 + y23) / 2,\n\t\tx234 = (x23 + x34) / 2,\n\t\ty234 = (y23 + y34) / 2,\n\t\tx1234 = (x123 + x234) / 2,\n\t\ty1234 = (y123 + y234) / 2,\n\t\tdx = x4 - x1,\n\t\tdy = y4 - y1,\n\t\td2 = _abs((x2 - x4) * dy - (y2 - y4) * dx),\n\t\td3 = _abs((x3 - x4) * dy - (y3 - y4) * dx),\n\t\tlength;\n\tif (!points) {\n\t\tpoints = [x1, y1, x4, y4];\n\t\tindex = 2;\n\t}\n\tpoints.splice(index || points.length - 2, 0, x1234, y1234);\n\tif ((d2 + d3) * (d2 + d3) > threshold * (dx * dx + dy * dy)) {\n\t\tlength = points.length;\n\t\tbezierToPoints(x1, y1, x12, y12, x123, y123, x1234, y1234, threshold, points, index);\n\t\tbezierToPoints(x1234, y1234, x234, y234, x34, y34, x4, y4, threshold, points, index + 2 + (points.length - length));\n\t}\n\treturn points;\n}\n\n/*\nfunction getAngleBetweenPoints(x0, y0, x1, y1, x2, y2) { //angle between 3 points in radians\n\tvar dx1 = x1 - x0,\n\t\tdy1 = y1 - y0,\n\t\tdx2 = x2 - x1,\n\t\tdy2 = y2 - y1,\n\t\tdx3 = x2 - x0,\n\t\tdy3 = y2 - y0,\n\t\ta = dx1 * dx1 + dy1 * dy1,\n\t\tb = dx2 * dx2 + dy2 * dy2,\n\t\tc = dx3 * dx3 + dy3 * dy3;\n\treturn Math.acos( (a + b - c) / _sqrt(4 * a * b) );\n},\n*/\n\n//pointsToSegment() doesn't handle flat coordinates (where y is always 0) the way we need (the resulting control points are always right on top of the anchors), so this function basically makes the control points go directly up and down, varying in length based on the curviness (more curvy, further control points)\nexport function flatPointsToSegment(points, curviness=1) {\n\tlet x = points[0],\n\t\ty = 0,\n\t\tsegment = [x, y],\n\t\ti = 2;\n\tfor (; i < points.length; i+=2) {\n\t\tsegment.push(\n\t\t\tx,\n\t\t\ty,\n\t\t\tpoints[i],\n\t\t\t(y = (points[i] - x) * curviness / 2),\n\t\t\t(x = points[i]),\n\t\t\t-y\n\t\t);\n\t}\n\treturn segment;\n}\n\n//points is an array of x/y points, like [x, y, x, y, x, y]\nexport function pointsToSegment(points, curviness) {\n\t//points = simplifyPoints(points, tolerance);\n\t_abs(points[0] - points[2]) < 1e-4 && _abs(points[1] - points[3]) < 1e-4 && (points = points.slice(2)); // if the first two points are super close, dump the first one.\n\tlet l = points.length-2,\n\t\tx = +points[0],\n\t\ty = +points[1],\n\t\tnextX = +points[2],\n\t\tnextY = +points[3],\n\t\tsegment = [x, y, x, y],\n\t\tdx2 = nextX - x,\n\t\tdy2 = nextY - y,\n\t\tclosed = Math.abs(points[l] - x) < 0.001 && Math.abs(points[l+1] - y) < 0.001,\n\t\tprevX, prevY, i, dx1, dy1, r1, r2, r3, tl, mx1, mx2, mxm, my1, my2, mym;\n\tif (closed) { // if the start and end points are basically on top of each other, close the segment by adding the 2nd point to the end, and the 2nd-to-last point to the beginning (we'll remove them at the end, but this allows the curvature to look perfect)\n\t\tpoints.push(nextX, nextY);\n\t\tnextX = x;\n\t\tnextY = y;\n\t\tx = points[l-2];\n\t\ty = points[l-1];\n\t\tpoints.unshift(x, y);\n\t\tl+=4;\n\t}\n\tcurviness = (curviness || curviness === 0) ? +curviness : 1;\n\tfor (i = 2; i < l; i+=2) {\n\t\tprevX = x;\n\t\tprevY = y;\n\t\tx = nextX;\n\t\ty = nextY;\n\t\tnextX = +points[i+2];\n\t\tnextY = +points[i+3];\n\t\tif (x === nextX && y === nextY) {\n\t\t\tcontinue;\n\t\t}\n\t\tdx1 = dx2;\n\t\tdy1 = dy2;\n\t\tdx2 = nextX - x;\n\t\tdy2 = nextY - y;\n\t\tr1 = _sqrt(dx1 * dx1 + dy1 * dy1); // r1, r2, and r3 correlate x and y (and z in the future). Basically 2D or 3D hypotenuse\n\t\tr2 = _sqrt(dx2 * dx2 + dy2 * dy2);\n\t\tr3 =  _sqrt((dx2 / r2 + dx1 / r1) ** 2 + (dy2 / r2 + dy1 / r1) ** 2);\n\t\ttl = ((r1 + r2) * curviness * 0.25) / r3;\n\t\tmx1 = x - (x - prevX) * (r1 ? tl / r1 : 0);\n\t\tmx2 = x + (nextX - x) * (r2 ? tl / r2 : 0);\n\t\tmxm = x - (mx1 + (((mx2 - mx1) * ((r1 * 3 / (r1 + r2)) + 0.5) / 4) || 0));\n\t\tmy1 = y - (y - prevY) * (r1 ? tl / r1 : 0);\n\t\tmy2 = y + (nextY - y) * (r2 ? tl / r2 : 0);\n\t\tmym = y - (my1 + (((my2 - my1) * ((r1 * 3 / (r1 + r2)) + 0.5) / 4) || 0));\n\t\tif (x !== prevX || y !== prevY) {\n\t\t\tsegment.push(\n\t\t\t\t_round(mx1 + mxm),  // first control point\n\t\t\t\t_round(my1 + mym),\n\t\t\t\t_round(x),          // anchor\n\t\t\t\t_round(y),\n\t\t\t\t_round(mx2 + mxm),  // second control point\n\t\t\t\t_round(my2 + mym)\n\t\t\t);\n\t\t}\n\t}\n\tx !== nextX || y !== nextY || segment.length < 4 ? segment.push(_round(nextX), _round(nextY), _round(nextX), _round(nextY)) : (segment.length -= 2);\n\tif (segment.length === 2) { // only one point!\n\t\tsegment.push(x, y, x, y, x, y);\n\t} else if (closed) {\n\t\tsegment.splice(0, 6);\n\t\tsegment.length = segment.length - 6;\n\t}\n\treturn segment;\n}\n\n//returns the squared distance between an x/y coordinate and a segment between x1/y1 and x2/y2\nfunction pointToSegDist(x, y, x1, y1, x2, y2) {\n\tlet dx = x2 - x1,\n\t\tdy = y2 - y1,\n\t\tt;\n\tif (dx || dy) {\n\t\tt = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\n\t\tif (t > 1) {\n\t\t\tx1 = x2;\n\t\t\ty1 = y2;\n\t\t} else if (t > 0) {\n\t\t\tx1 += dx * t;\n\t\t\ty1 += dy * t;\n\t\t}\n\t}\n\treturn (x - x1) ** 2 + (y - y1) ** 2;\n}\n\nfunction simplifyStep(points, first, last, tolerance, simplified) {\n\tlet maxSqDist = tolerance,\n\t\tfirstX = points[first],\n\t\tfirstY = points[first+1],\n\t\tlastX = points[last],\n\t\tlastY = points[last+1],\n\t\tindex, i, d;\n\tfor (i = first + 2; i < last; i += 2) {\n\t\td = pointToSegDist(points[i], points[i+1], firstX, firstY, lastX, lastY);\n\t\tif (d > maxSqDist) {\n\t\t\tindex = i;\n\t\t\tmaxSqDist = d;\n\t\t}\n\t}\n\tif (maxSqDist > tolerance) {\n\t\tindex - first > 2 && simplifyStep(points, first, index, tolerance, simplified);\n\t\tsimplified.push(points[index], points[index+1]);\n\t\tlast - index > 2 && simplifyStep(points, index, last, tolerance, simplified);\n\t}\n}\n\n//points is an array of x/y values like [x, y, x, y, x, y]\nexport function simplifyPoints(points, tolerance) {\n\tlet prevX = parseFloat(points[0]),\n\t\tprevY = parseFloat(points[1]),\n\t\ttemp = [prevX, prevY],\n\t\tl = points.length - 2,\n\t\ti, x, y, dx, dy, result, last;\n\ttolerance = (tolerance || 1) ** 2;\n\tfor (i = 2; i < l; i += 2) {\n\t\tx = parseFloat(points[i]);\n\t\ty = parseFloat(points[i+1]);\n\t\tdx = prevX - x;\n\t\tdy = prevY - y;\n\t\tif (dx * dx + dy * dy > tolerance) {\n\t\t\ttemp.push(x, y);\n\t\t\tprevX = x;\n\t\t\tprevY = y;\n\t\t}\n\t}\n\ttemp.push(parseFloat(points[l]), parseFloat(points[l+1]));\n\tlast = temp.length - 2;\n\tresult = [temp[0], temp[1]];\n\tsimplifyStep(temp, 0, last, tolerance, result);\n\tresult.push(temp[last], temp[last+1]);\n\treturn result;\n}\n\nfunction getClosestProgressOnBezier(iterations, px, py, start, end, slices, x0, y0, x1, y1, x2, y2, x3, y3) {\n\tlet inc = (end - start) / slices,\n\t\tbest = 0,\n\t\tt = start,\n\t\tx, y, d, dx, dy, inv;\n\t_bestDistance = _largeNum;\n\twhile (t <= end) {\n\t\tinv = 1 - t;\n\t\tx = inv * inv * inv * x0 + 3 * inv * inv * t * x1 + 3 * inv * t * t * x2 + t * t * t * x3;\n\t\ty = inv * inv * inv * y0 + 3 * inv * inv * t * y1 + 3 * inv * t * t * y2 + t * t * t * y3;\n\t\tdx = x - px;\n\t\tdy = y - py;\n\t\td = dx * dx + dy * dy;\n\t\tif (d < _bestDistance) {\n\t\t\t_bestDistance = d;\n\t\t\tbest = t;\n\t\t}\n\t\tt += inc;\n\t}\n\treturn (iterations > 1) ? getClosestProgressOnBezier(iterations - 1, px, py, Math.max(best - inc, 0), Math.min(best + inc, 1), slices, x0, y0, x1, y1, x2, y2, x3, y3) : best;\n}\n\nexport function getClosestData(rawPath, x, y, slices) { //returns an object with the closest j, i, and t (j is the segment index, i is the index of the point in that segment, and t is the time/progress along that bezier)\n\tlet closest = {j:0, i:0, t:0},\n\t\tbestDistance = _largeNum,\n\t\ti, j, t, segment;\n\tfor (j = 0; j < rawPath.length; j++) {\n\t\tsegment = rawPath[j];\n\t\tfor (i = 0; i < segment.length; i+=6) {\n\t\t\tt = getClosestProgressOnBezier(1, x, y, 0, 1, slices || 20, segment[i], segment[i+1], segment[i+2], segment[i+3], segment[i+4], segment[i+5], segment[i+6], segment[i+7]);\n\t\t\tif (bestDistance > _bestDistance) {\n\t\t\t\tbestDistance = _bestDistance;\n\t\t\t\tclosest.j = j;\n\t\t\t\tclosest.i = i;\n\t\t\t\tclosest.t = t;\n\t\t\t}\n\t\t}\n\t}\n\treturn closest;\n}\n\n//subdivide a Segment closest to a specific x,y coordinate\nexport function subdivideSegmentNear(x, y, segment, slices, iterations) {\n\tlet l = segment.length,\n\t\tbestDistance = _largeNum,\n\t\tbestT = 0,\n\t\tbestSegmentIndex = 0,\n\t\tt, i;\n\tslices = slices || 20;\n\titerations = iterations || 3;\n\tfor (i = 0; i < l; i += 6) {\n\t\tt = getClosestProgressOnBezier(1, x, y, 0, 1, slices, segment[i], segment[i+1], segment[i+2], segment[i+3], segment[i+4], segment[i+5], segment[i+6], segment[i+7]);\n\t\tif (bestDistance > _bestDistance) {\n\t\t\tbestDistance = _bestDistance;\n\t\t\tbestT = t;\n\t\t\tbestSegmentIndex = i;\n\t\t}\n\t}\n\tt = getClosestProgressOnBezier(iterations, x, y, bestT - 0.05, bestT + 0.05, slices, segment[bestSegmentIndex], segment[bestSegmentIndex+1], segment[bestSegmentIndex+2], segment[bestSegmentIndex+3], segment[bestSegmentIndex+4], segment[bestSegmentIndex+5], segment[bestSegmentIndex+6], segment[bestSegmentIndex+7]);\n\tsubdivideSegment(segment, bestSegmentIndex, t);\n\treturn bestSegmentIndex + 6;\n}\n\n/*\nTakes any of the following and converts it to an all Cubic Bezier SVG data string:\n- A <path> data string like \"M0,0 L2,4 v20,15 H100\"\n- A RawPath, like [[x, y, x, y, x, y, x, y][[x, y, x, y, x, y, x, y]]\n- A Segment, like [x, y, x, y, x, y, x, y]\n\nNote: all numbers are rounded down to the closest 0.001 to minimize memory, maximize speed, and avoid odd numbers like 1e-13\n*/\nexport function rawPathToString(rawPath) {\n\tif (_isNumber(rawPath[0])) { //in case a segment is passed in instead\n\t\trawPath = [rawPath];\n\t}\n\tlet result = \"\",\n\t\tl = rawPath.length,\n\t\tsl, s, i, segment;\n\tfor (s = 0; s < l; s++) {\n\t\tsegment = rawPath[s];\n\t\tresult += \"M\" + _round(segment[0]) + \",\" + _round(segment[1]) + \" C\";\n\t\tsl = segment.length;\n\t\tfor (i = 2; i < sl; i++) {\n\t\t\tresult += _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i]) + \" \";\n\t\t}\n\t\tif (segment.closed) {\n\t\t\tresult += \"z\";\n\t\t}\n\t}\n\treturn result;\n}\n\n/*\n// takes a segment with coordinates [x, y, x, y, ...] and converts the control points into angles and lengths [x, y, angle, length, angle, length, x, y, angle, length, ...] so that it animates more cleanly and avoids odd breaks/kinks. For example, if you animate from 1 o'clock to 6 o'clock, it'd just go directly/linearly rather than around. So the length would be very short in the middle of the tween.\nexport function cpCoordsToAngles(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tx, y, i;\n\tfor (i = 0; i < segment.length; i+=6) {\n\t\tx = segment[i+2] - segment[i];\n\t\ty = segment[i+3] - segment[i+1];\n\t\tresult[i+2] = Math.atan2(y, x);\n\t\tresult[i+3] = Math.sqrt(x * x + y * y);\n\t\tx = segment[i+6] - segment[i+4];\n\t\ty = segment[i+7] - segment[i+5];\n\t\tresult[i+4] = Math.atan2(y, x);\n\t\tresult[i+5] = Math.sqrt(x * x + y * y);\n\t}\n\treturn result;\n}\n\n// takes a segment that was converted with cpCoordsToAngles() to have angles and lengths instead of coordinates for the control points, and converts it BACK into coordinates.\nexport function cpAnglesToCoords(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tlength = segment.length,\n\t\trnd = 1000,\n\t\tangle, l, i, j;\n\tfor (i = 0; i < length; i+=6) {\n\t\tangle = segment[i+2];\n\t\tl = segment[i+3]; //length\n\t\tresult[i+2] = (((segment[i] + Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+3] = (((segment[i+1] + Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t\tangle = segment[i+4];\n\t\tl = segment[i+5]; //length\n\t\tresult[i+4] = (((segment[i+6] - Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+5] = (((segment[i+7] - Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t}\n\treturn result;\n}\n\n//adds an \"isSmooth\" array to each segment and populates it with a boolean value indicating whether or not it's smooth (the control points have basically the same slope). For any smooth control points, it converts the coordinates into angle (x, in radians) and length (y) and puts them into the same index value in a smoothData array.\nexport function populateSmoothData(rawPath) {\n\tlet j = rawPath.length,\n\t\tsmooth, segment, x, y, x2, y2, i, l, a, a2, isSmooth, smoothData;\n\twhile (--j > -1) {\n\t\tsegment = rawPath[j];\n\t\tisSmooth = segment.isSmooth = segment.isSmooth || [0, 0, 0, 0];\n\t\tsmoothData = segment.smoothData = segment.smoothData || [0, 0, 0, 0];\n\t\tisSmooth.length = 4;\n\t\tl = segment.length - 2;\n\t\tfor (i = 6; i < l; i += 6) {\n\t\t\tx = segment[i] - segment[i - 2];\n\t\t\ty = segment[i + 1] - segment[i - 1];\n\t\t\tx2 = segment[i + 2] - segment[i];\n\t\t\ty2 = segment[i + 3] - segment[i + 1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tsmooth = (Math.abs(a - a2) < 0.09);\n\t\t\tif (smooth) {\n\t\t\t\tsmoothData[i - 2] = a;\n\t\t\t\tsmoothData[i + 2] = a2;\n\t\t\t\tsmoothData[i - 1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[i + 3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t}\n\t\t\tisSmooth.push(smooth, smooth, 0, 0, smooth, smooth);\n\t\t}\n\t\t//if the first and last points are identical, check to see if there's a smooth transition. We must handle this a bit differently due to their positions in the array.\n\t\tif (segment[l] === segment[0] && segment[l+1] === segment[1]) {\n\t\t\tx = segment[0] - segment[l-2];\n\t\t\ty = segment[1] - segment[l-1];\n\t\t\tx2 = segment[2] - segment[0];\n\t\t\ty2 = segment[3] - segment[1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tif (Math.abs(a - a2) < 0.09) {\n\t\t\t\tsmoothData[l-2] = a;\n\t\t\t\tsmoothData[2] = a2;\n\t\t\t\tsmoothData[l-1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t\tisSmooth[l-2] = isSmooth[l-1] = true; //don't change indexes 2 and 3 because we'll trigger everything from the END, and this will optimize file size a bit.\n\t\t\t}\n\t\t}\n\t}\n\treturn rawPath;\n}\nexport function pointToScreen(svgElement, point) {\n\tif (arguments.length < 2) { //by default, take the first set of coordinates in the path as the point\n\t\tlet rawPath = getRawPath(svgElement);\n\t\tpoint = svgElement.ownerSVGElement.createSVGPoint();\n\t\tpoint.x = rawPath[0][0];\n\t\tpoint.y = rawPath[0][1];\n\t}\n\treturn point.matrixTransform(svgElement.getScreenCTM());\n}\n// takes a <path> and normalizes all of its coordinates to values between 0 and 1\nexport function normalizePath(path) {\n  path = gsap.utils.toArray(path);\n  if (!path[0].hasAttribute(\"d\")) {\n    path = gsap.utils.toArray(path[0].children);\n  }\n  if (path.length > 1) {\n    path.forEach(normalizePath);\n    return path;\n  }\n  let _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n      _scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n      d = path[0].getAttribute(\"d\"),\n      a = d.replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp),\n      nums = a.filter(n => !isNaN(n)).map(n => +n),\n      normalize = gsap.utils.normalize(Math.min(...nums), Math.max(...nums)),\n      finals = a.map(val => isNaN(val) ? val : normalize(+val)),\n      s = \"\",\n      prevWasCommand;\n  finals.forEach((value, i) => {\n    let isCommand = isNaN(value)\n    s += (isCommand && i ? \" \" : prevWasCommand || !i ? \"\" : \",\") + value;\n    prevWasCommand = isCommand;\n  });\n  path[0].setAttribute(\"d\", s);\n}\n*/", "/*!\n * MorphSVGPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { getRawPath, reverseSegment, stringToRawPath, rawPathToString, convertToPath } from \"./utils/paths.js\";\n\nlet gsap, _toArray, _lastLinkedAnchor, _doc, _coreInitted, PluginClass,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isFunction = value => typeof(value) === \"function\",\n\t_atan2 = Math.atan2,\n\t_cos = Math.cos,\n\t_sin = Math.sin,\n\t_sqrt = Math.sqrt,\n\t_PI = Math.PI,\n\t_2PI = _PI * 2,\n\t_angleMin = _PI * 0.3,\n\t_angleMax = _PI * 0.7,\n\t_bigNum = 1e20,\n\t_numExp = /[-+=\\.]*\\d+[\\.e\\-\\+]*\\d*[e\\-\\+]*\\d*/gi, //finds any numbers, including ones that start with += or -=, negative numbers, and ones in scientific notation like 1e-8.\n\t_selectorExp = /(^[#\\.][a-z]|[a-y][a-z])/i,\n\t_commands = /[achlmqstvz]/i,\n\t_log = message => console && console.warn(message),\n\t_bonusValidated = 1, //<name>MorphSVGPlugin</name>\n\t_getAverageXY = segment => {\n\t\tlet l = segment.length,\n\t\t\tx = 0,\n\t\t\ty = 0,\n\t\t\ti;\n\t\tfor (i = 0; i < l; i++) {\n\t\t\tx += segment[i++];\n\t\t\ty += segment[i];\n\t\t}\n\t\treturn [x / (l / 2), y / (l / 2)];\n\t},\n\t_getSize = segment => { //rough estimate of the bounding box (based solely on the anchors) of a single segment. sets \"size\", \"centerX\", and \"centerY\" properties on the bezier array itself, and returns the size (width * height)\n\t\tlet l = segment.length,\n\t\t\txMax = segment[0],\n\t\t\txMin = xMax,\n\t\t\tyMax = segment[1],\n\t\t\tyMin = yMax,\n\t\t\tx, y, i;\n\t\tfor (i = 6; i < l; i+=6) {\n\t\t\tx = segment[i];\n\t\t\ty = segment[i+1];\n\t\t\tif (x > xMax) {\n\t\t\t\txMax = x;\n\t\t\t} else if (x < xMin) {\n\t\t\t\txMin = x;\n\t\t\t}\n\t\t\tif (y > yMax) {\n\t\t\t\tyMax = y;\n\t\t\t} else if (y < yMin) {\n\t\t\t\tyMin = y;\n\t\t\t}\n\t\t}\n\t\tsegment.centerX = (xMax + xMin) / 2;\n\t\tsegment.centerY = (yMax + yMin) / 2;\n\t\treturn (segment.size = (xMax - xMin) * (yMax - yMin));\n\t},\n\t_getTotalSize = (rawPath, samplesPerBezier = 3) => { //rough estimate of the bounding box of the entire list of Bezier segments (based solely on the anchors). sets \"size\", \"centerX\", and \"centerY\" properties on the bezier array itself, and returns the size (width * height)\n\t\tlet j = rawPath.length,\n\t\t\txMax = rawPath[0][0],\n\t\t\txMin = xMax,\n\t\t\tyMax = rawPath[0][1],\n\t\t\tyMin = yMax,\n\t\t\tinc = 1 / samplesPerBezier,\n\t\t\tl, x, y, i, segment, k, t, inv, x1, y1, x2, x3, x4, y2, y3, y4;\n\t\twhile (--j > -1) {\n\t\t\tsegment = rawPath[j];\n\t\t\tl = segment.length;\n\t\t\tfor (i = 6; i < l; i+=6) {\n\t\t\t\tx1 = segment[i];\n\t\t\t\ty1 = segment[i+1];\n\t\t\t\tx2 = segment[i+2] - x1;\n\t\t\t\ty2 = segment[i+3] - y1;\n\t\t\t\tx3 = segment[i+4] - x1;\n\t\t\t\ty3 = segment[i+5] - y1;\n\t\t\t\tx4 = segment[i+6] - x1;\n\t\t\t\ty4 = segment[i+7] - y1;\n\t\t\t\tk = samplesPerBezier;\n\t\t\t\twhile (--k > -1) {\n\t\t\t\t\tt = inc * k;\n\t\t\t\t\tinv = 1 - t;\n\t\t\t\t\tx = (t * t * x4 + 3 * inv * (t * x3 + inv * x2)) * t + x1;\n\t\t\t\t\ty = (t * t * y4 + 3 * inv * (t * y3 + inv * y2)) * t + y1;\n\t\t\t\t\tif (x > xMax) {\n\t\t\t\t\t\txMax = x;\n\t\t\t\t\t} else if (x < xMin) {\n\t\t\t\t\t\txMin = x;\n\t\t\t\t\t}\n\t\t\t\t\tif (y > yMax) {\n\t\t\t\t\t\tyMax = y;\n\t\t\t\t\t} else if (y < yMin) {\n\t\t\t\t\t\tyMin = y;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\trawPath.centerX = (xMax + xMin) / 2;\n\t\trawPath.centerY = (yMax + yMin) / 2;\n\t\trawPath.left = xMin;\n\t\trawPath.width = (xMax - xMin);\n\t\trawPath.top = yMin;\n\t\trawPath.height = (yMax - yMin);\n\t\treturn (rawPath.size = (xMax - xMin) * (yMax - yMin));\n\t},\n\t_sortByComplexity = (a, b) => b.length - a.length,\n\t_sortBySize = (a, b) => {\n\t\tlet sizeA = a.size || _getSize(a),\n\t\t\tsizeB = b.size || _getSize(b);\n\t\treturn (Math.abs(sizeB - sizeA) < (sizeA + sizeB) / 20) ? (b.centerX - a.centerX) || (b.centerY - a.centerY) : sizeB - sizeA; //if the size is within 10% of each other, prioritize position from left to right, then top to bottom.\n\t},\n\t_offsetSegment = (segment, shapeIndex) => {\n\t\tlet a = segment.slice(0),\n\t\t\tl = segment.length,\n\t\t\twrap = l - 2,\n\t\t\ti, index;\n\t\tshapeIndex = shapeIndex | 0;\n\t\tfor (i = 0; i < l; i++) {\n\t\t\tindex = (i + shapeIndex) % wrap;\n\t\t\tsegment[i++] = a[index];\n\t\t\tsegment[i] = a[index+1];\n\t\t}\n\t},\n\t_getTotalMovement = (sb, eb, shapeIndex, offsetX, offsetY) => {\n\t\tlet l = sb.length,\n\t\t\td = 0,\n\t\t\twrap = l - 2,\n\t\t\tindex, i, x, y;\n\t\tshapeIndex *= 6;\n\t\tfor (i = 0; i < l; i += 6) {\n\t\t\tindex = (i + shapeIndex) % wrap;\n\t\t\ty = sb[index] - (eb[i] - offsetX);\n\t\t\tx = sb[index+1] - (eb[i+1] - offsetY);\n\t\t\td += _sqrt(x * x + y * y);\n\t\t}\n\t\treturn d;\n\t},\n\t_getClosestShapeIndex = (sb, eb, checkReverse) => { //finds the index in a closed cubic bezier array that's closest to the angle provided (angle measured from the center or average x/y).\n\t\tlet l = sb.length,\n\t\t\tsCenter = _getAverageXY(sb), //when comparing distances, adjust the coordinates as if the shapes are centered with each other.\n\t\t\teCenter = _getAverageXY(eb),\n\t\t\toffsetX = eCenter[0] - sCenter[0],\n\t\t\toffsetY = eCenter[1] - sCenter[1],\n\t\t\tmin = _getTotalMovement(sb, eb, 0, offsetX, offsetY),\n\t\t\tminIndex = 0,\n\t\t\tcopy, d, i;\n\t\tfor (i = 6; i < l; i += 6) {\n\t\t\td = _getTotalMovement(sb, eb, i / 6, offsetX, offsetY);\n\t\t\tif (d < min) {\n\t\t\t\tmin = d;\n\t\t\t\tminIndex = i;\n\t\t\t}\n\t\t}\n\t\tif (checkReverse) {\n\t\t\tcopy = sb.slice(0);\n\t\t\treverseSegment(copy);\n\t\t\tfor (i = 6; i < l; i += 6) {\n\t\t\t\td = _getTotalMovement(copy, eb, i / 6, offsetX, offsetY);\n\t\t\t\tif (d < min) {\n\t\t\t\t\tmin = d;\n\t\t\t\t\tminIndex = -i;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn minIndex / 6;\n\t},\n\t_getClosestAnchor = (rawPath, x, y) => { //finds the x/y of the anchor that's closest to the provided x/y coordinate (returns an array, like [x, y]). The bezier should be the top-level type that contains an array for each segment.\n\t\tlet j = rawPath.length,\n\t\t\tclosestDistance = _bigNum,\n\t\t\tclosestX = 0,\n\t\t\tclosestY = 0,\n\t\t\tsegment, dx, dy, d, i, l;\n\t\twhile (--j > -1) {\n\t\t\tsegment = rawPath[j];\n\t\t\tl = segment.length;\n\t\t\tfor (i = 0; i < l; i += 6) {\n\t\t\t\tdx = segment[i] - x;\n\t\t\t\tdy = segment[i+1] - y;\n\t\t\t\td = _sqrt(dx * dx + dy * dy);\n\t\t\t\tif (d < closestDistance) {\n\t\t\t\t\tclosestDistance = d;\n\t\t\t\t\tclosestX = segment[i];\n\t\t\t\t\tclosestY = segment[i+1];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn [closestX, closestY];\n\t},\n\t_getClosestSegment = (bezier, pool, startIndex, sortRatio, offsetX, offsetY) => { //matches the bezier to the closest one in a pool (array) of beziers, assuming they are in order of size and we shouldn't drop more than 20% of the size, otherwise prioritizing location (total distance to the center). Extracts the segment out of the pool array and returns it.\n\t\tlet l = pool.length,\n\t\t\tindex = 0,\n\t\t\tminSize = Math.min(bezier.size || _getSize(bezier), pool[startIndex].size || _getSize(pool[startIndex])) * sortRatio, //limit things based on a percentage of the size of either the bezier or the next element in the array, whichever is smaller.\n\t\t\tmin = _bigNum,\n\t\t\tcx = bezier.centerX + offsetX,\n\t\t\tcy = bezier.centerY + offsetY,\n\t\t\tsize, i, dx, dy, d;\n\t\tfor (i = startIndex; i < l; i++) {\n\t\t\tsize = pool[i].size || _getSize(pool[i]);\n\t\t\tif (size < minSize) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tdx = pool[i].centerX - cx;\n\t\t\tdy = pool[i].centerY - cy;\n\t\t\td = _sqrt(dx * dx + dy * dy);\n\t\t\tif (d < min) {\n\t\t\t\tindex = i;\n\t\t\t\tmin = d;\n\t\t\t}\n\t\t}\n\t\td = pool[index];\n\t\tpool.splice(index, 1);\n\t\treturn d;\n\t},\n\t_subdivideSegmentQty = (segment, quantity) => {\n\t\tlet tally = 0,\n\t\t\tmax = 0.999999,\n\t\t\tl = segment.length,\n\t\t\tnewPointsPerSegment = quantity / ((l - 2) / 6),\n\t\t\tax, ay, cp1x, cp1y, cp2x, cp2y, bx, by,\n\t\t\tx1, y1, x2, y2, i, t;\n\t\tfor (i = 2; i < l; i += 6) {\n\t\t\ttally += newPointsPerSegment;\n\t\t\twhile (tally > max) { //compare with 0.99999 instead of 1 in order to prevent rounding errors\n\t\t\t\tax = segment[i-2];\n\t\t\t\tay = segment[i-1];\n\t\t\t\tcp1x = segment[i];\n\t\t\t\tcp1y = segment[i+1];\n\t\t\t\tcp2x = segment[i+2];\n\t\t\t\tcp2y = segment[i+3];\n\t\t\t\tbx = segment[i+4];\n\t\t\t\tby = segment[i+5];\n\t\t\t\tt = 1 / ((Math.floor(tally) || 1) + 1); //progress along the bezier (value between 0 and 1)\n\t\t\t\tx1 = ax + (cp1x - ax) * t;\n\t\t\t\tx2 = cp1x + (cp2x - cp1x) * t;\n\t\t\t\tx1 += (x2 - x1) * t;\n\t\t\t\tx2 += ((cp2x + (bx - cp2x) * t) - x2) * t;\n\t\t\t\ty1 = ay + (cp1y - ay) * t;\n\t\t\t\ty2 = cp1y + (cp2y - cp1y) * t;\n\t\t\t\ty1 += (y2 - y1) * t;\n\t\t\t\ty2 += ((cp2y + (by - cp2y) * t) - y2) * t;\n\t\t\t\tsegment.splice(i, 4,\n\t\t\t\t\tax + (cp1x - ax) * t,   //first control point\n\t\t\t\t\tay + (cp1y - ay) * t,\n\t\t\t\t\tx1,                     //second control point\n\t\t\t\t\ty1,\n\t\t\t\t\tx1 + (x2 - x1) * t,     //new fabricated anchor on line\n\t\t\t\t\ty1 + (y2 - y1) * t,\n\t\t\t\t\tx2,                     //third control point\n\t\t\t\t\ty2,\n\t\t\t\t\tcp2x + (bx - cp2x) * t, //fourth control point\n\t\t\t\t\tcp2y + (by - cp2y) * t\n\t\t\t\t);\n\t\t\t\ti += 6;\n\t\t\t\tl += 6;\n\t\t\t\ttally--;\n\t\t\t}\n\t\t}\n\t\treturn segment;\n\t},\n\t_equalizeSegmentQuantity = (start, end, shapeIndex, map, fillSafe) => { //returns an array of shape indexes, 1 for each segment.\n\t\tlet dif = end.length - start.length,\n\t\t\tlonger = dif > 0 ? end : start,\n\t\t\tshorter = dif > 0 ? start : end,\n\t\t\tadded = 0,\n\t\t\tsortMethod = (map === \"complexity\") ? _sortByComplexity : _sortBySize,\n\t\t\tsortRatio = (map === \"position\") ? 0 : (typeof(map) === \"number\") ? map : 0.8,\n\t\t\ti = shorter.length,\n\t\t\tshapeIndices = (typeof(shapeIndex) === \"object\" && shapeIndex.push) ? shapeIndex.slice(0) : [shapeIndex],\n\t\t\treverse = (shapeIndices[0] === \"reverse\" || shapeIndices[0] < 0),\n\t\t\tlog = (shapeIndex === \"log\"),\n\t\t\teb, sb, b, x, y, offsetX, offsetY;\n\t\tif (!shorter[0]) {\n\t\t\treturn;\n\t\t}\n\t\tif (longer.length > 1) {\n\t\t\tstart.sort(sortMethod);\n\t\t\tend.sort(sortMethod);\n\t\t\toffsetX = longer.size || _getTotalSize(longer); //ensures centerX and centerY are defined (used below).\n\t\t\toffsetX = shorter.size || _getTotalSize(shorter);\n\t\t\toffsetX = longer.centerX - shorter.centerX;\n\t\t\toffsetY = longer.centerY - shorter.centerY;\n\t\t\tif (sortMethod === _sortBySize) {\n\t\t\t\tfor (i = 0; i < shorter.length; i++) {\n\t\t\t\t\tlonger.splice(i, 0, _getClosestSegment(shorter[i], longer, i, sortRatio, offsetX, offsetY));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (dif) {\n\t\t\tif (dif < 0) {\n\t\t\t\tdif = -dif;\n\t\t\t}\n\t\t\tif (longer[0].length > shorter[0].length) { //since we use shorter[0] as the one to map the origination point of any brand new fabricated segments, do any subdividing first so that there are more points to choose from (if necessary)\n\t\t\t\t_subdivideSegmentQty(shorter[0], ((longer[0].length - shorter[0].length)/6) | 0);\n\t\t\t}\n\t\t\ti = shorter.length;\n\t\t\twhile (added < dif) {\n\t\t\t\tx = longer[i].size || _getSize(longer[i]); //just to ensure centerX and centerY are calculated which we use on the next line.\n\t\t\t\tb = _getClosestAnchor(shorter, longer[i].centerX, longer[i].centerY);\n\t\t\t\tx = b[0];\n\t\t\t\ty = b[1];\n\t\t\t\tshorter[i++] = [x, y, x, y, x, y, x, y];\n\t\t\t\tshorter.totalPoints += 8;\n\t\t\t\tadded++;\n\t\t\t}\n\t\t}\n\t\tfor (i = 0; i < start.length; i++) {\n\t\t\teb = end[i];\n\t\t\tsb = start[i];\n\t\t\tdif = eb.length - sb.length;\n\t\t\tif (dif < 0) {\n\t\t\t\t_subdivideSegmentQty(eb, (-dif/6) | 0);\n\t\t\t} else if (dif > 0) {\n\t\t\t\t_subdivideSegmentQty(sb, (dif/6) | 0);\n\t\t\t}\n\t\t\tif (reverse && fillSafe !== false && !sb.reversed) {\n\t\t\t\treverseSegment(sb);\n\t\t\t}\n\t\t\tshapeIndex = (shapeIndices[i] || shapeIndices[i] === 0) ? shapeIndices[i] : \"auto\";\n\t\t\tif (shapeIndex) {\n\t\t\t\t//if start shape is closed, find the closest point to the start/end, and re-organize the bezier points accordingly so that the shape morphs in a more intuitive way.\n\t\t\t\tif (sb.closed || (Math.abs(sb[0] - sb[sb.length - 2]) < 0.5 && Math.abs(sb[1] - sb[sb.length - 1]) < 0.5)) {\n\t\t\t\t\tif (shapeIndex === \"auto\" || shapeIndex === \"log\") {\n\t\t\t\t\t\tshapeIndices[i] = shapeIndex = _getClosestShapeIndex(sb, eb, (!i || fillSafe === false));\n\t\t\t\t\t\tif (shapeIndex < 0) {\n\t\t\t\t\t\t\treverse = true;\n\t\t\t\t\t\t\treverseSegment(sb);\n\t\t\t\t\t\t\tshapeIndex = -shapeIndex;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t_offsetSegment(sb, shapeIndex * 6);\n\n\t\t\t\t\t} else if (shapeIndex !== \"reverse\") {\n\t\t\t\t\t\tif (i && shapeIndex < 0) { //only happens if an array is passed as shapeIndex and a negative value is defined for an index beyond 0. Very rare, but helpful sometimes.\n\t\t\t\t\t\t\treverseSegment(sb);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t_offsetSegment(sb, (shapeIndex < 0 ? -shapeIndex : shapeIndex) * 6);\n\t\t\t\t\t}\n\t\t\t\t\t//otherwise, if it's not a closed shape, consider reversing it if that would make the overall travel less\n\t\t\t\t} else if (!reverse && (shapeIndex === \"auto\" && (Math.abs(eb[0] - sb[0]) + Math.abs(eb[1] - sb[1]) + Math.abs(eb[eb.length - 2] - sb[sb.length - 2]) + Math.abs(eb[eb.length - 1] - sb[sb.length - 1]) > Math.abs(eb[0] - sb[sb.length - 2]) + Math.abs(eb[1] - sb[sb.length - 1]) + Math.abs(eb[eb.length - 2] - sb[0]) + Math.abs(eb[eb.length - 1] - sb[1])) || (shapeIndex % 2))) {\n\t\t\t\t\treverseSegment(sb);\n\t\t\t\t\tshapeIndices[i] = -1;\n\t\t\t\t\treverse = true;\n\t\t\t\t} else if (shapeIndex === \"auto\") {\n\t\t\t\t\tshapeIndices[i] = 0;\n\t\t\t\t} else if (shapeIndex === \"reverse\") {\n\t\t\t\t\tshapeIndices[i] = -1;\n\t\t\t\t}\n\t\t\t\tif (sb.closed !== eb.closed) { //if one is closed and one isn't, don't close either one otherwise the tweening will look weird (but remember, the beginning and final states will honor the actual values, so this only affects the inbetween state)\n\t\t\t\t\tsb.closed = eb.closed = false;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tlog && _log(\"shapeIndex:[\" + shapeIndices.join(\",\") + \"]\");\n\t\tstart.shapeIndex = shapeIndices;\n\t\treturn shapeIndices;\n\t},\n\t_pathFilter = (a, shapeIndex, map, precompile, fillSafe) => {\n\t\tlet start = stringToRawPath(a[0]),\n\t\t\tend = stringToRawPath(a[1]);\n\t\tif (!_equalizeSegmentQuantity(start, end, (shapeIndex || shapeIndex === 0) ? shapeIndex : \"auto\", map, fillSafe)) {\n\t\t\treturn; //malformed path data or null target\n\t\t}\n\t\ta[0] = rawPathToString(start);\n\t\ta[1] = rawPathToString(end);\n\t\tif (precompile === \"log\" || precompile === true) {\n\t\t\t_log('precompile:[\"' + a[0] + '\",\"' + a[1] + '\"]');\n\t\t}\n\t},\n\t_offsetPoints = (text, offset) => {\n\t\tif (!offset) {\n\t\t\treturn text;\n\t\t}\n\t\tlet a = text.match(_numExp) || [],\n\t\t\tl = a.length,\n\t\t\ts = \"\",\n\t\t\tinc, i, j;\n\t\tif (offset === \"reverse\") {\n\t\t\ti = l-1;\n\t\t\tinc = -2;\n\t\t} else {\n\t\t\ti = (((parseInt(offset, 10) || 0) * 2 + 1) + l * 100) % l;\n\t\t\tinc = 2;\n\t\t}\n\t\tfor (j = 0; j < l; j += 2) {\n\t\t\ts += a[i-1] + \",\" + a[i] + \" \";\n\t\t\ti = (i + inc) % l;\n\t\t}\n\t\treturn s;\n\t},\n\t//adds a certain number of points while maintaining the polygon/polyline shape (so that the start/end values can have a matching quantity of points to animate). Returns the revised string.\n\t_equalizePointQuantity = (a, quantity) => {\n\t\tlet tally = 0,\n\t\t\tx = parseFloat(a[0]),\n\t\t\ty = parseFloat(a[1]),\n\t\t\ts = x + \",\" + y + \" \",\n\t\t\tmax = 0.999999,\n\t\t\tnewPointsPerSegment, i, l, j, factor, nextX, nextY;\n\t\tl = a.length;\n\t\tnewPointsPerSegment = quantity * 0.5 / (l * 0.5 - 1);\n\t\tfor (i = 0; i < l-2; i += 2) {\n\t\t\ttally += newPointsPerSegment;\n\t\t\tnextX = parseFloat(a[i+2]);\n\t\t\tnextY = parseFloat(a[i+3]);\n\t\t\tif (tally > max) { //compare with 0.99999 instead of 1 in order to prevent rounding errors\n\t\t\t\tfactor = 1 / (Math.floor(tally) + 1);\n\t\t\t\tj = 1;\n\t\t\t\twhile (tally > max) {\n\t\t\t\t\ts += (x + (nextX - x) * factor * j).toFixed(2) + \",\" + (y + (nextY - y) * factor * j).toFixed(2) + \" \";\n\t\t\t\t\ttally--;\n\t\t\t\t\tj++;\n\t\t\t\t}\n\t\t\t}\n\t\t\ts += nextX + \",\" + nextY + \" \";\n\t\t\tx = nextX;\n\t\t\ty = nextY;\n\t\t}\n\t\treturn s;\n\t},\n\t_pointsFilter = a => {\n\t\tlet startNums = a[0].match(_numExp) || [],\n\t\t\tendNums = a[1].match(_numExp) || [],\n\t\t\tdif = endNums.length - startNums.length;\n\t\tif (dif > 0) {\n\t\t\ta[0] = _equalizePointQuantity(startNums, dif);\n\t\t} else {\n\t\t\ta[1] = _equalizePointQuantity(endNums, -dif);\n\t\t}\n\t},\n\t_buildPointsFilter = shapeIndex => !isNaN(shapeIndex) ? a => {\n\t\t\t_pointsFilter(a);\n\t\t\ta[1] = _offsetPoints(a[1], parseInt(shapeIndex, 10));\n\t\t} : _pointsFilter,\n\t_parseShape = (shape, forcePath, target) => {\n\t\tlet isString = typeof(shape) === \"string\",\n\t\t\te, type;\n\t\tif (!isString || _selectorExp.test(shape) || (shape.match(_numExp) || []).length < 3) {\n\t\t\te = _toArray(shape)[0];\n\t\t\tif (e) {\n\t\t\t\ttype = (e.nodeName + \"\").toUpperCase();\n\t\t\t\tif (forcePath && type !== \"PATH\") { //if we were passed an element (or selector text for an element) that isn't a path, convert it.\n\t\t\t\t\te = convertToPath(e, false);\n\t\t\t\t\ttype = \"PATH\";\n\t\t\t\t}\n\t\t\t\tshape = e.getAttribute(type === \"PATH\" ? \"d\" : \"points\") || \"\";\n\t\t\t\tif (e === target) { //if the shape matches the target element, the user wants to revert to the original which should have been stored in the data-original attribute\n\t\t\t\t\tshape = e.getAttributeNS(null, \"data-original\") || shape;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t_log(\"WARNING: invalid morph to: \" + shape);\n\t\t\t\tshape = false;\n\t\t\t}\n\t\t}\n\t\treturn shape;\n\t},\n\t//adds an \"isSmooth\" array to each segment and populates it with a boolean value indicating whether or not it's smooth (the control points have basically the same slope). For any smooth control points, it converts the coordinates into angle (x, in radians) and length (y) and puts them into the same index value in a smoothData array.\n\t_populateSmoothData = (rawPath, tolerance) => {\n\t\tlet j = rawPath.length,\n\t\t\tlimit = 0.2 * (tolerance || 1),\n\t\t\tsmooth, segment, x, y, x2, y2, i, l, a, a2, isSmooth, smoothData;\n\t\twhile (--j > -1) {\n\t\t\tsegment = rawPath[j];\n\t\t\tisSmooth = segment.isSmooth = segment.isSmooth || [0, 0, 0, 0];\n\t\t\tsmoothData = segment.smoothData = segment.smoothData || [0, 0, 0, 0];\n\t\t\tisSmooth.length = 4;\n\t\t\tl = segment.length - 2;\n\t\t\tfor (i = 6; i < l; i += 6) {\n\t\t\t\tx = segment[i] - segment[i - 2];\n\t\t\t\ty = segment[i + 1] - segment[i - 1];\n\t\t\t\tx2 = segment[i + 2] - segment[i];\n\t\t\t\ty2 = segment[i + 3] - segment[i + 1];\n\t\t\t\ta = _atan2(y, x);\n\t\t\t\ta2 = _atan2(y2, x2);\n\t\t\t\tsmooth = (Math.abs(a - a2) < limit);\n\t\t\t\tif (smooth) {\n\t\t\t\t\tsmoothData[i - 2] = a;\n\t\t\t\t\tsmoothData[i + 2] = a2;\n\t\t\t\t\tsmoothData[i - 1] = _sqrt(x * x + y * y);\n\t\t\t\t\tsmoothData[i + 3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t\t}\n\t\t\t\tisSmooth.push(smooth, smooth, 0, 0, smooth, smooth);\n\t\t\t}\n\t\t\t//if the first and last points are identical, check to see if there's a smooth transition. We must handle this a bit differently due to their positions in the array.\n\t\t\tif (segment[l] === segment[0] && segment[l+1] === segment[1]) {\n\t\t\t\tx = segment[0] - segment[l-2];\n\t\t\t\ty = segment[1] - segment[l-1];\n\t\t\t\tx2 = segment[2] - segment[0];\n\t\t\t\ty2 = segment[3] - segment[1];\n\t\t\t\ta = _atan2(y, x);\n\t\t\t\ta2 = _atan2(y2, x2);\n\t\t\t\tif (Math.abs(a - a2) < limit) {\n\t\t\t\t\tsmoothData[l-2] = a;\n\t\t\t\t\tsmoothData[2] = a2;\n\t\t\t\t\tsmoothData[l-1] = _sqrt(x * x + y * y);\n\t\t\t\t\tsmoothData[3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t\t\tisSmooth[l-2] = isSmooth[l-1] = true; //don't change indexes 2 and 3 because we'll trigger everything from the END, and this will optimize file size a bit.\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn rawPath;\n\t},\n\t_parseOriginFactors = v => {\n\t\tlet a = v.trim().split(\" \"),\n\t\t\tx = ~v.indexOf(\"left\") ? 0 : ~v.indexOf(\"right\") ? 100 : isNaN(parseFloat(a[0])) ? 50 : parseFloat(a[0]),\n\t\t\ty = ~v.indexOf(\"top\") ? 0 : ~v.indexOf(\"bottom\") ? 100 : isNaN(parseFloat(a[1])) ? 50 : parseFloat(a[1]);\n\t\treturn {x:x / 100, y:y / 100};\n\t},\n\t_shortAngle = dif => (dif !== dif % _PI) ? dif + ((dif < 0) ? _2PI : -_2PI) : dif,\n\t_morphMessage = \"Use MorphSVGPlugin.convertToPath() to convert to a path before morphing.\",\n\t_tweenRotation = function(start, end, i, linkedPT) {\n\t\tlet so = this._origin,              //starting origin\n\t\t\teo = this._eOrigin,             //ending origin\n\t\t\tdx = start[i] - so.x,\n\t\t\tdy = start[i+1] - so.y,\n\t\t\td = _sqrt(dx * dx + dy * dy),   //length from starting origin to starting point\n\t\t\tsa = _atan2(dy, dx),\n\t\t\tangleDif, short;\n\t\tdx = end[i] - eo.x;\n\t\tdy = end[i+1] - eo.y;\n\t\tangleDif = _atan2(dy, dx) - sa;\n\t\tshort = _shortAngle(angleDif);\n\t\t//in the case of control points, we ALWAYS link them to their anchor so that they don't get torn apart and rotate the opposite direction. If it's not a control point, we look at the most recently linked point as long as they're within a certain rotational range of each other.\n\t\tif (!linkedPT && _lastLinkedAnchor && Math.abs(short + _lastLinkedAnchor.ca) < _angleMin) {\n\t\t\tlinkedPT = _lastLinkedAnchor;\n\t\t}\n\t\treturn (this._anchorPT = _lastLinkedAnchor = {\n\t\t\t_next:this._anchorPT,\n\t\t\tt:start,\n\t\t\tsa:sa,                              //starting angle\n\t\t\tca:(linkedPT && short * linkedPT.ca < 0 && Math.abs(short) > _angleMax) ? angleDif : short,  //change in angle\n\t\t\tsl:d,                               //starting length\n\t\t\tcl:_sqrt(dx * dx + dy * dy) - d,    //change in length\n\t\t\ti:i\n\t\t});\n\t},\n\t_initCore = required => {\n\t\tgsap = _getGSAP();\n\t\tPluginClass = PluginClass || (gsap && gsap.plugins.morphSVG);\n\t\tif (gsap && PluginClass) {\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_doc = document;\n\t\t\tPluginClass.prototype._tweenRotation = _tweenRotation;\n\t\t\t_coreInitted = 1;\n\t\t} else if (required) {\n\t\t\t_log(\"Please gsap.registerPlugin(MorphSVGPlugin)\");\n\t\t}\n\t};\n\n\nexport const MorphSVGPlugin = {\n\tversion: \"3.13.0\",\n\tname: \"morphSVG\",\n\trawVars: 1, // otherwise \"render\" would be interpreted as a function-based value.\n\tregister(core, Plugin) {\n\t\tgsap = core;\n\t\tPluginClass = Plugin;\n\t\t_initCore();\n\t},\n\tinit(target, value, tween, index, targets) {\n\t\t_coreInitted || _initCore(1);\n\t\tif (!value) {\n\t\t\t_log(\"invalid shape\");\n\t\t\treturn false;\n\t\t}\n\t\t_isFunction(value) && (value = value.call(tween, index, target, targets));\n\t\tlet type, p, pt, shape, isPoly, shapeIndex, map, startSmooth, endSmooth, start, end, i, j, l, startSeg, endSeg, precompiled, sData, eData, originFactors, useRotation, offset;\n\t\tif (typeof(value) === \"string\" || value.getBBox || value[0]) {\n\t\t\tvalue = {shape:value};\n\t\t} else if (typeof(value) === \"object\") { // if there are any function-based values, parse them here (and make a copy of the object so we're not modifying the original)\n\t\t\ttype = {};\n\t\t\tfor (p in value) {\n\t\t\t\ttype[p] = _isFunction(value[p]) && p !== \"render\" ? value[p].call(tween, index, target, targets) : value[p];\n\t\t\t}\n\t\t\tvalue = type;\n\t\t}\n\t\tlet cs = target.nodeType ? window.getComputedStyle(target) : {},\n\t\t\tfill = cs.fill + \"\",\n\t\t\tfillSafe = !(fill === \"none\" || (fill.match(_numExp) || [])[3] === \"0\" || cs.fillRule === \"evenodd\"),\n\t\t\torigins = (value.origin || \"50 50\").split(\",\");\n\t\ttype = (target.nodeName + \"\").toUpperCase();\n\t\tisPoly = (type === \"POLYLINE\" || type === \"POLYGON\");\n\t\tif (type !== \"PATH\" && !isPoly && !value.prop) {\n\t\t\t_log(\"Cannot morph a <\" + type + \"> element. \" + _morphMessage);\n\t\t\treturn false;\n\t\t}\n\t\tp = (type === \"PATH\") ? \"d\" : \"points\";\n\t\tif (!value.prop && !_isFunction(target.setAttribute)) {\n\t\t\treturn false;\n\t\t}\n\t\tshape = _parseShape(value.shape || value.d || value.points || \"\", (p === \"d\"), target);\n\t\tif (isPoly && _commands.test(shape)) {\n\t\t\t_log(\"A <\" + type + \"> cannot accept path data. \" + _morphMessage);\n\t\t\treturn false;\n\t\t}\n\t\tshapeIndex = (value.shapeIndex || value.shapeIndex === 0) ? value.shapeIndex : \"auto\";\n\t\tmap = value.map || MorphSVGPlugin.defaultMap;\n\t\tthis._prop = value.prop;\n\t\tthis._render = value.render || MorphSVGPlugin.defaultRender;\n\t\tthis._apply = (\"updateTarget\" in value) ? value.updateTarget : MorphSVGPlugin.defaultUpdateTarget;\n\t\tthis._rnd = Math.pow(10, isNaN(value.precision) ? 2 : +value.precision);\n\t\tthis._tween = tween;\n\t\tif (shape) {\n\t\t\tthis._target = target;\n\t\t\tprecompiled = (typeof(value.precompile) === \"object\");\n\t\t\tstart = this._prop ? target[this._prop] : target.getAttribute(p);\n\t\t\tif (!this._prop && !target.getAttributeNS(null, \"data-original\")) {\n\t\t\t\ttarget.setAttributeNS(null, \"data-original\", start); //record the original state in a data-original attribute so that we can revert to it later.\n\t\t\t}\n\t\t\tif (p === \"d\" || this._prop) {\n\t\t\t\tstart = stringToRawPath(precompiled ? value.precompile[0] : start);\n\t\t\t\tend = stringToRawPath(precompiled ? value.precompile[1] : shape);\n\t\t\t\tif (!precompiled && !_equalizeSegmentQuantity(start, end, shapeIndex, map, fillSafe)) {\n\t\t\t\t\treturn false; //malformed path data or null target\n\t\t\t\t}\n\t\t\t\tif (value.precompile === \"log\" || value.precompile === true) {\n\t\t\t\t\t_log('precompile:[\"' + rawPathToString(start) + '\",\"' + rawPathToString(end) + '\"]');\n\t\t\t\t}\n\t\t\t\tuseRotation = (value.type || MorphSVGPlugin.defaultType) !== \"linear\";\n\t\t\t\tif (useRotation) {\n\t\t\t\t\tstart = _populateSmoothData(start, value.smoothTolerance);\n\t\t\t\t\tend = _populateSmoothData(end, value.smoothTolerance);\n\t\t\t\t\tif (!start.size) {\n\t\t\t\t\t\t_getTotalSize(start); //adds top/left/width/height values\n\t\t\t\t\t}\n\t\t\t\t\tif (!end.size) {\n\t\t\t\t\t\t_getTotalSize(end);\n\t\t\t\t\t}\n\t\t\t\t\toriginFactors = _parseOriginFactors(origins[0]);\n\t\t\t\t\tthis._origin = start.origin = {x:start.left + originFactors.x * start.width, y:start.top + originFactors.y * start.height};\n\t\t\t\t\tif (origins[1]) {\n\t\t\t\t\t\toriginFactors = _parseOriginFactors(origins[1]);\n\t\t\t\t\t}\n\t\t\t\t\tthis._eOrigin = {x:end.left + originFactors.x * end.width, y:end.top + originFactors.y * end.height};\n\t\t\t\t}\n\n\t\t\t\tthis._rawPath = target._gsRawPath =  start;\n\n\t\t\t\tj = start.length;\n\t\t\t\twhile (--j > -1) {\n\t\t\t\t\tstartSeg = start[j];\n\t\t\t\t\tendSeg = end[j];\n\t\t\t\t\tstartSmooth = startSeg.isSmooth || [];\n\t\t\t\t\tendSmooth = endSeg.isSmooth || [];\n\t\t\t\t\tl = startSeg.length;\n\t\t\t\t\t_lastLinkedAnchor = 0; //reset; we use _lastLinkedAnchor in the _tweenRotation() method to help make sure that close points don't get ripped apart and rotate opposite directions. Typically we want to go the shortest direction, but if the previous anchor is going a different direction, we override this logic (within certain thresholds)\n\t\t\t\t\tfor (i = 0; i < l; i+=2) {\n\t\t\t\t\t\tif (endSeg[i] !== startSeg[i] || endSeg[i+1] !== startSeg[i+1]) {\n\t\t\t\t\t\t\tif (useRotation) {\n\t\t\t\t\t\t\t\tif (startSmooth[i] && endSmooth[i]) { //if BOTH starting and ending values are smooth (meaning control points have basically the same slope), interpolate the rotation and length instead of the coordinates (this is what makes things smooth).\n\t\t\t\t\t\t\t\t\tsData = startSeg.smoothData;\n\t\t\t\t\t\t\t\t\teData = endSeg.smoothData;\n\t\t\t\t\t\t\t\t\toffset = i + ((i === l - 4) ? 7 - l : 5); //helps us accommodate wrapping (like if the end and start anchors are identical and the control points are smooth).\n\t\t\t\t\t\t\t\t\tthis._controlPT = {_next:this._controlPT, i:i, j:j, l1s:sData[i+1], l1c:eData[i+1] - sData[i+1], l2s:sData[offset], l2c:eData[offset] - sData[offset]};\n\t\t\t\t\t\t\t\t\tpt = this._tweenRotation(startSeg, endSeg, i+2);\n\t\t\t\t\t\t\t\t\tthis._tweenRotation(startSeg, endSeg, i, pt);\n\t\t\t\t\t\t\t\t\tthis._tweenRotation(startSeg, endSeg, offset-1, pt);\n\t\t\t\t\t\t\t\t\ti+=4;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis._tweenRotation(startSeg, endSeg, i);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tpt = this.add(startSeg, i, startSeg[i], endSeg[i], 0, 0, 0, 0, 0, 1);\n\t\t\t\t\t\t\t\tpt = this.add(startSeg, i+1, startSeg[i+1], endSeg[i+1], 0, 0, 0, 0, 0, 1) || pt;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tpt = this.add(target, \"setAttribute\", target.getAttribute(p) + \"\", shape + \"\", index, targets, 0, _buildPointsFilter(shapeIndex), p);\n\t\t\t}\n\n\t\t\tif (useRotation) {\n\t\t\t\tthis.add(this._origin, \"x\", this._origin.x, this._eOrigin.x, 0, 0, 0, 0, 0, 1);\n\t\t\t\tpt = this.add(this._origin, \"y\", this._origin.y, this._eOrigin.y, 0, 0, 0, 0, 0, 1);\n\t\t\t}\n\n\t\t\tif (pt) {\n\t\t\t\tthis._props.push(\"morphSVG\");\n\t\t\t\tpt.end = shape;\n\t\t\t\tpt.endProp = p;\n\t\t\t}\n\t\t}\n\t\treturn _bonusValidated;\n\t},\n\n\trender(ratio, data) {\n\t\tlet rawPath = data._rawPath,\n\t\t\tcontrolPT = data._controlPT,\n\t\t\tanchorPT = data._anchorPT,\n\t\t\trnd = data._rnd,\n\t\t\ttarget = data._target,\n\t\t\tpt = data._pt,\n\t\t\ts, space, easeInOut, segment, l, angle, i, j, x, y, sin, cos, offset;\n\t\twhile (pt) {\n\t\t\tpt.r(ratio, pt.d);\n\t\t\tpt = pt._next;\n\t\t}\n\t\tif (ratio === 1 && data._apply) {\n\t\t\tpt = data._pt;\n\t\t\twhile (pt) {\n\t\t\t\tif (pt.end) {\n\t\t\t\t\tif (data._prop) {\n\t\t\t\t\t\ttarget[data._prop] = pt.end;\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget.setAttribute(pt.endProp, pt.end); //make sure the end value is exactly as specified (in case we had to add fabricated points during the tween)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tpt = pt._next;\n\t\t\t}\n\t\t} else if (rawPath) {\n\n\t\t\t//rotationally position the anchors\n\t\t\twhile (anchorPT) {\n\t\t\t\tangle = anchorPT.sa + ratio * anchorPT.ca;\n\t\t\t\tl = anchorPT.sl + ratio * anchorPT.cl;    //length\n\t\t\t\tanchorPT.t[anchorPT.i] = data._origin.x + _cos(angle) * l;\n\t\t\t\tanchorPT.t[anchorPT.i + 1] = data._origin.y + _sin(angle) * l;\n\t\t\t\tanchorPT = anchorPT._next;\n\t\t\t}\n\n\t\t\t//smooth out the control points\n\t\t\teaseInOut = ratio < 0.5 ? 2 * ratio * ratio : (4 - 2 * ratio) * ratio - 1;\n\t\t\twhile (controlPT) {\n\t\t\t\ti = controlPT.i;\n\t\t\t\tsegment = rawPath[controlPT.j];\n\t\t\t\toffset = i + ((i === segment.length - 4) ? 7 - segment.length : 5); //accommodates wrapping around of smooth points, like if the start and end anchors are on top of each other and their handles are smooth.\n\t\t\t\tangle = _atan2(segment[offset] - segment[i+1], segment[offset-1] - segment[i]); //average the angles\n\t\t\t\tsin = _sin(angle);\n\t\t\t\tcos = _cos(angle);\n\t\t\t\tx = segment[i+2];\n\t\t\t\ty = segment[i+3];\n\t\t\t\tl = controlPT.l1s + easeInOut * controlPT.l1c;    //length\n\t\t\t\tsegment[i] = x - cos * l;\n\t\t\t\tsegment[i+1] = y - sin * l;\n\t\t\t\tl = controlPT.l2s + easeInOut * controlPT.l2c;\n\t\t\t\tsegment[offset-1] = x + cos * l;\n\t\t\t\tsegment[offset] = y + sin * l;\n\t\t\t\tcontrolPT = controlPT._next;\n\t\t\t}\n\n\t\t\ttarget._gsRawPath = rawPath;\n\n\t\t\tif (data._apply) {\n\t\t\t\ts = \"\";\n\t\t\t\tspace = \" \";\n\t\t\t\tfor (j = 0; j < rawPath.length; j++) {\n\t\t\t\t\tsegment = rawPath[j];\n\t\t\t\t\tl = segment.length;\n\t\t\t\t\ts += \"M\" + (((segment[0] * rnd) | 0) / rnd) + space + (((segment[1] * rnd) | 0) / rnd) + \" C\";\n\t\t\t\t\tfor (i = 2; i < l; i++) { //this is actually faster than just doing a join() on the array, possibly because the numbers have so many decimal places\n\t\t\t\t\t\ts += (((segment[i] * rnd) | 0) / rnd) + space;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (data._prop) {\n\t\t\t\t\ttarget[data._prop] = s;\n\t\t\t\t} else {\n\t\t\t\t\ttarget.setAttribute(\"d\", s);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tdata._render && rawPath && data._render.call(data._tween, rawPath, target);\n\t},\n\tkill(property) {\n\t\tthis._pt = this._rawPath = 0;\n\t},\n\tgetRawPath: getRawPath,\n\tstringToRawPath: stringToRawPath,\n\trawPathToString: rawPathToString,\n\tnormalizeStrings(shape1, shape2, {shapeIndex, map}) {\n\t\tlet result = [shape1, shape2];\n\t\t_pathFilter(result, shapeIndex, map);\n\t\treturn result;\n\t},\n\tpathFilter: _pathFilter,\n\tpointsFilter: _pointsFilter,\n\tgetTotalSize: _getTotalSize,\n\tequalizeSegmentQuantity: _equalizeSegmentQuantity,\n\tconvertToPath: (targets, swap) => _toArray(targets).map(target => convertToPath(target, swap !== false)),\n\tdefaultType: \"linear\",\n\tdefaultUpdateTarget: true,\n\tdefaultMap: \"size\"\n};\n\n_getGSAP() && gsap.registerPlugin(MorphSVGPlugin);\n\nexport { MorphSVGPlugin as default };"], "names": ["_isString", "value", "_svgPathExp", "_numbersExp", "_scientific", "_selectorExp", "_DEG2RAD", "Math", "PI", "_sin", "sin", "_cos", "cos", "_abs", "abs", "_sqrt", "sqrt", "_isNumber", "_round", "round", "reverseSegment", "segment", "y", "i", "reverse", "length", "reversed", "_typeAttrs", "rect", "circle", "ellipse", "line", "convertToPath", "element", "swap", "data", "x", "r", "ry", "path", "rcirc", "rycirc", "points", "w", "h", "x2", "x3", "x4", "x5", "x6", "y2", "y3", "y4", "y5", "y6", "attr", "type", "tagName", "toLowerCase", "circ", "getBBox", "_createPath", "e", "ignore", "name", "document", "createElementNS", "slice", "call", "attributes", "nodeName", "indexOf", "setAttributeNS", "nodeValue", "_attrToObj", "attrs", "props", "split", "obj", "getAttribute", "rx", "width", "height", "join", "cx", "cy", "x1", "y1", "match", "shift", "setAttribute", "rawPathToString", "_gsRawPath", "stringToRawPath", "parentNode", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "arcToSegment", "lastX", "lastY", "angle", "largeArcFlag", "sweepFlag", "angleRad", "cosAngle", "sinAngle", "TWOPI", "dx2", "dy2", "x1_sq", "y1_sq", "radiiCheck", "rx_sq", "ry_sq", "sq", "coef", "cx1", "cy1", "ux", "uy", "vx", "vy", "temp", "angleStart", "acos", "angleExtent", "isNaN", "segments", "ceil", "rawPath", "angleIncrement", "controlLength", "ma", "mb", "mc", "md", "push", "d", "sx", "sy", "ex", "ey", "difX", "difY", "j", "command", "isRelative", "startX", "startY", "beziers", "prevCommand", "flag1", "flag2", "a", "replace", "m", "n", "relativeX", "relativeY", "elements", "errorMessage", "console", "log", "toUpperCase", "closed", "substr", "char<PERSON>t", "pop", "totalPoints", "sl", "s", "result", "l", "_getGSAP", "gsap", "window", "registerPlugin", "_isFunction", "_log", "message", "warn", "_getAverageXY", "_getSize", "xMax", "xMin", "yMax", "yMin", "centerX", "centerY", "size", "_getTotalSize", "samplesPerBezier", "k", "t", "inv", "inc", "left", "top", "_sortByComplexity", "b", "_sortBySize", "sizeA", "sizeB", "_offsetSegment", "shapeIndex", "index", "wrap", "_getTotalMovement", "sb", "eb", "offsetX", "offsetY", "_getClosestShapeIndex", "checkReverse", "copy", "sCenter", "eCenter", "min", "minIndex", "_getClosestAnchor", "dx", "dy", "closestDistance", "closestX", "closestY", "_getClosestSegment", "bezier", "pool", "startIndex", "sortRatio", "minSize", "splice", "_subdivideSegmentQty", "quantity", "ax", "ay", "cp1x", "cp1y", "cp2x", "cp2y", "bx", "by", "tally", "newPointsPerSegment", "floor", "_equalizeSegmentQuantity", "start", "end", "map", "fillSafe", "dif", "longer", "shorter", "added", "sortMethod", "shapeIndices", "sort", "_pathFilter", "precompile", "_equalizePointQuantity", "factor", "nextX", "nextY", "parseFloat", "toFixed", "_pointsFilter", "startNums", "_numExp", "endNums", "_buildPointsFilter", "_offsetPoints", "text", "offset", "parseInt", "_populateSmoothData", "tolerance", "smooth", "a2", "isSmooth", "smoothData", "limit", "_atan2", "_parseOriginFactors", "v", "trim", "_tweenRotation", "linkedPT", "angleDif", "short", "so", "this", "_origin", "eo", "_e<PERSON><PERSON><PERSON>", "sa", "_shortAngle", "_PI", "_2PI", "_lastLinkedAnchor", "ca", "_angleMin", "_anchorPT", "_next", "_angleMax", "cl", "_initCore", "required", "PluginClass", "plugins", "morphSVG", "_toArray", "utils", "toArray", "prototype", "_coreInitted", "atan2", "_commands", "_morphMessage", "MorphSVGPlugin", "version", "rawVars", "register", "core", "Plugin", "init", "target", "tween", "targets", "p", "pt", "shape", "isPoly", "startSmooth", "endSmooth", "startSeg", "endSeg", "precompiled", "sData", "eData", "originFactors", "useRotation", "cs", "nodeType", "getComputedStyle", "fill", "fillRule", "origins", "origin", "prop", "_parseShape", "forcePath", "test", "getAttributeNS", "defaultMap", "_prop", "_render", "render", "defaultRender", "_apply", "updateTarget", "defaultUpdateTarget", "_rnd", "pow", "precision", "_tween", "_target", "defaultType", "smoothTolerance", "_rawPath", "_controlPT", "l1s", "l1c", "l2s", "l2c", "add", "_props", "endProp", "ratio", "easeInOut", "controlPT", "anchorPT", "rnd", "_pt", "kill", "getRawPath", "querySelector", "_gsPath", "_dirty", "normalizeStrings", "shape1", "shape2", "pathFilter", "pointsFilter", "getTotalSize", "equalizeSegmentQuantity"], "mappings": ";;;;;;;;;6MAsBa,SAAZA,EAAYC,SAA2B,iBAAXA,MAZzBC,EAAc,mDACjBC,EAAc,0CACdC,EAAc,gCACdC,EAAe,4BACfC,EAAWC,KAAKC,GAAK,IAErBC,EAAOF,KAAKG,IACZC,EAAOJ,KAAKK,IACZC,EAAON,KAAKO,IACZC,EAAQR,KAAKS,KAIbC,EAAY,SAAZA,UAAYhB,SAA2B,iBAAXA,GAM5BiB,EAAS,SAATA,OAASjB,UAAUM,KAAKY,MAFT,IAEelB,GAFf,KAEwD,GAwFjE,SAASmB,eAAeC,OAE7BC,EADGC,EAAI,MAERF,EAAQG,UACDD,EAAIF,EAAQI,OAAQF,GAAK,EAC/BD,EAAID,EAAQE,GACZF,EAAQE,GAAKF,EAAQE,EAAE,GACvBF,EAAQE,EAAE,GAAKD,EAEhBD,EAAQK,UAAYL,EAAQK,SAK7B,IAcCC,EAAa,CACZC,KAAK,yBACLC,OAAO,UACPC,QAAQ,cACRC,KAAK,eAaA,SAASC,cAAcC,EAASC,OAGrCC,EAAMC,EAAGd,EAAGe,EAAGC,EAAIC,EAAMC,EAAOC,EAAQC,EAAQC,EAAGC,EAAGC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAF3FC,EAAOvB,EAAQwB,QAAQC,cAC1BC,EAAO,oBAEK,SAATH,GAAoBvB,EAAQ2B,SAGhCrB,EAtCiB,SAAdsB,YAAeC,EAAGC,OAInBC,EAHGzB,EAAO0B,SAASC,gBAAgB,6BAA8B,QACjEX,EAAO,GAAGY,MAAMC,KAAKN,EAAEO,YACvB9C,EAAIgC,EAAK9B,WAEVsC,EAAS,IAAMA,EAAS,KACV,IAALxC,GACRyC,EAAOT,EAAKhC,GAAG+C,SAASZ,cACpBK,EAAOQ,QAAQ,IAAMP,EAAO,KAAO,GACtCzB,EAAKiC,eAAe,KAAMR,EAAMT,EAAKhC,GAAGkD,kBAGnClC,EA0BDsB,CAAY5B,EAAS,qDAC5BsB,EAnBa,SAAbmB,WAAcZ,EAAGa,WACZC,EAAQD,EAAQA,EAAME,MAAM,KAAO,GACtCC,EAAM,GACNvD,EAAIqD,EAAMnD,QACG,IAALF,GACRuD,EAAIF,EAAMrD,KAAOuC,EAAEiB,aAAaH,EAAMrD,KAAO,SAEvCuD,EAYDJ,CAAWzC,EAASN,EAAW6B,IACzB,SAATA,GACHnB,EAAIkB,EAAKyB,GACT1C,EAAKiB,EAAKjB,IAAMD,EAChBD,EAAImB,EAAKnB,EACTd,EAAIiC,EAAKjC,EACTqB,EAAIY,EAAK0B,MAAY,EAAJ5C,EACjBO,EAAIW,EAAK2B,OAAc,EAAL5C,EAYjBH,EAXGE,GAAKC,EAWD,KANPW,GAFAF,GADAD,EAAKV,EAAIC,GACCM,GAEAN,GAMQ,KAJlBc,EAAK7B,EAAIgB,GAIoB,MAH7Bc,EAAKD,EAAKP,GAG+B,KAAO,CAACK,EAFjDI,EAAKD,EAAKd,EAAKqB,EALfX,EAAKD,EAAKV,EAAIsB,EAMdL,EAAKF,EAAKd,EACuDS,EAAIO,EAAIP,GAAMA,EAAKD,GAAM,EAAGQ,EAAIR,GAAMC,EAAKD,GAAM,EAAGQ,EAAIR,EAAIQ,EAV7HT,EAAKT,EAAIC,GAAK,EAAIsB,GAUmHL,EAAIlB,EAAGiB,EAAIjB,EAAGgB,EAAIhB,EAAGgB,GAAMA,EAAKD,GAAM,EAAGf,EAAGe,GAAMC,EAAKD,GAAM,EAAGf,EAAGe,EAAIf,EAL5Mc,EAAK5B,EAAIgB,GAAM,EAAIqB,GAKgMd,EAAIvB,EAAGwB,EAAIxB,EAAGwB,GAAMC,EAAKD,GAAM,EAAGxB,EAAGyB,GAAMA,EAAKD,GAAM,EAAGxB,EAAGyB,EAAIzB,EAAG0B,EAAI1B,EAAG2B,EAAIC,EAAID,EAAIE,GAAIgC,KAAK,KAAO,IAElT,KAAO/C,EAAIO,GAAK,IAAMrB,EAAI,KAAOsB,EAAI,MAASD,EAAK,MAASC,EAAK,KAAOD,EAAI,KAGjE,WAATa,GAA8B,YAATA,GAG9Bf,EAFY,WAATe,GACHnB,EAAIC,EAAKiB,EAAKlB,GACDsB,GAEbtB,EAAIkB,EAAKyB,IACT1C,EAAKiB,EAAKjB,IACIqB,GAKfxB,EAAO,MAHPC,EAAImB,EAAK6B,IAGO/C,GAAK,KAFrBf,EAAIiC,EAAK8B,IAEsB,KAAO,CAACjD,EAAEC,EAAGf,EAAImB,EAAQL,GADxDI,EAAQH,EAAIsB,GACuDrC,EAAIgB,EAAIF,EAAGd,EAAIgB,EAAIF,EAAII,EAAOlB,EAAIgB,EAAIF,EAAIC,EAAGf,EAAImB,EAAQL,EAAIC,EAAGf,EAAGc,EAAIC,EAAGf,EAAImB,EAAQL,EAAII,EAAOlB,EAAIgB,EAAIF,EAAGd,EAAIgB,EAAIF,EAAII,EAAOlB,EAAIgB,EAAIF,EAAIC,EAAGf,EAAImB,EAAQL,EAAIC,EAAGf,GAAG6D,KAAK,KAAO,KAChO,SAAT3B,EACVrB,EAAO,IAAMoB,EAAK+B,GAAK,IAAM/B,EAAKgC,GAAK,KAAOhC,EAAKV,GAAK,IAAMU,EAAKL,GAChD,aAATM,GAAgC,YAATA,IAIjCrB,EAAO,KAFPC,GADAM,GAAUT,EAAQ8C,aAAa,UAAY,IAAIS,MAAMrF,IAAgB,IAC1DsF,SAEM,KADjBnE,EAAIoB,EAAO+C,SACgB,KAAO/C,EAAOyC,KAAK,KACjC,YAAT3B,IACHrB,GAAQ,IAAMC,EAAI,IAAMd,EAAI,MAG9BiB,EAAKmD,aAAa,IAAKC,gBAAgBpD,EAAKqD,WAAaC,gBAAgB1D,KACrED,GAAQD,EAAQ6D,aACnB7D,EAAQ6D,WAAWC,aAAaxD,EAAMN,GACtCA,EAAQ6D,WAAWE,YAAY/D,IAEzBM,GAxDCN,EAiaT,SAASgE,aAAaC,EAAOC,EAAOnB,EAAI1C,EAAI8D,EAAOC,EAAcC,EAAWlE,EAAGd,MAC1E4E,IAAU9D,GAAK+D,IAAU7E,GAG7B0D,EAAKnE,EAAKmE,GACV1C,EAAKzB,EAAKyB,OACNiE,EAAYH,EAAQ,IAAO9F,EAC9BkG,EAAW7F,EAAK4F,GAChBE,EAAWhG,EAAK8F,GAChB/F,EAAKD,KAAKC,GACVkG,EAAa,EAALlG,EACRmG,GAAOT,EAAQ9D,GAAK,EACpBwE,GAAOT,EAAQ7E,GAAK,EACpBgE,EAAMkB,EAAWG,EAAMF,EAAWG,EAClCrB,GAAOkB,EAAWE,EAAMH,EAAWI,EACnCC,EAAQvB,EAAKA,EACbwB,EAAQvB,EAAKA,EACbwB,EAAaF,GAAS7B,EAAKA,GAAM8B,GAASxE,EAAKA,GAC/B,EAAbyE,IACH/B,EAAKjE,EAAMgG,GAAc/B,EACzB1C,EAAKvB,EAAMgG,GAAczE,OAEtB0E,EAAQhC,EAAKA,EAChBiC,EAAQ3E,EAAKA,EACb4E,GAAOF,EAAQC,EAAUD,EAAQF,EAAUG,EAAQJ,IAAYG,EAAQF,EAAUG,EAAQJ,GACtFK,EAAK,IACRA,EAAK,OAEFC,GAASd,IAAiBC,GAAc,EAAI,GAAKvF,EAAMmG,GAC1DE,EAAepC,EAAKO,EAAMjD,EAApB6E,EACNE,GAAgB/E,EAAKgD,EAAMN,EAArBmC,EAGN/B,EAAYoB,EAAWY,EAAMX,EAAWY,GAFjCnB,EAAQ9D,GAAK,EAGpBiD,EAAYoB,EAAWW,EAAMZ,EAAWa,GAFjClB,EAAQ7E,GAAK,EAGpBgG,GAAMhC,EAAK8B,GAAOpC,EAClBuC,GAAMhC,EAAK8B,GAAO/E,EAClBkF,IAAOlC,EAAK8B,GAAOpC,EACnByC,IAAOlC,EAAK8B,GAAO/E,EACnBoF,EAAOJ,EAAKA,EAAKC,EAAKA,EACtBI,GAAeJ,EAAK,GAAM,EAAI,GAAKhH,KAAKqH,KAAKN,EAAKvG,EAAM2G,IACxDG,GAAgBP,EAAKG,EAAKF,EAAKC,EAAK,GAAM,EAAI,GAAKjH,KAAKqH,MAAMN,EAAKE,EAAKD,EAAKE,GAAM1G,EAAM2G,GAAQF,EAAKA,EAAKC,EAAKA,KACjHK,MAAMD,KAAiBA,EAAcrH,IAChC8F,GAA2B,EAAduB,EACjBA,GAAenB,EACLJ,GAAauB,EAAc,IACrCA,GAAenB,GAEhBiB,GAAcjB,EACdmB,GAAenB,MASdnF,EARGwG,EAAWxH,KAAKyH,KAAKnH,EAAKgH,IAAgBnB,EAAQ,IACrDuB,EAAU,GACVC,EAAiBL,EAAcE,EAC/BI,EAAgB,EAAI,EAAI1H,EAAKyH,EAAiB,IAAM,EAAIvH,EAAKuH,EAAiB,IAC9EE,EAAK5B,EAAWxB,EAChBqD,EAAK5B,EAAWzB,EAChBsD,EAAK7B,GAAYnE,EACjBiG,EAAK/B,EAAWlE,MAEZf,EAAI,EAAGA,EAAIwG,EAAUxG,IAEzB+D,EAAK3E,EADLyF,EAAQuB,EAAapG,EAAI2G,GAEzB3C,EAAK9E,EAAK2F,GACVkB,EAAK3G,EAAKyF,GAAS8B,GACnBX,EAAK9G,EAAK2F,GACV6B,EAAQO,KAAKlD,EAAK6C,EAAgB5C,EAAIA,EAAK4C,EAAgB7C,EAAIgC,EAAKa,EAAgBZ,EAAIA,EAAKY,EAAgBb,EAAIA,EAAIC,OAGjHhG,EAAI,EAAGA,EAAI0G,EAAQxG,OAAQF,GAAG,EAClC+D,EAAK2C,EAAQ1G,GACbgE,EAAK0C,EAAQ1G,EAAE,GACf0G,EAAQ1G,GAAK+D,EAAK8C,EAAK7C,EAAK+C,EAAKlD,EACjC6C,EAAQ1G,EAAE,GAAK+D,EAAK+C,EAAK9C,EAAKgD,EAAKlD,SAEpC4C,EAAQ1G,EAAE,GAAKa,EACf6F,EAAQ1G,EAAE,GAAKD,EACR2G,GAID,SAASpC,gBAAgB4C,GAUvB,SAAP1G,GAAgB2G,EAAIC,EAAIC,EAAIC,GAC3BC,GAAQF,EAAKF,GAAM,EACnBK,GAAQF,EAAKF,GAAM,EACnBtH,EAAQmH,KAAKE,EAAKI,EAAMH,EAAKI,EAAMH,EAAKE,EAAMD,EAAKE,EAAMH,EAAIC,OAJ9DtH,EAAGyH,EAAG5G,EAAGd,EAAG2H,EAASC,EAAY7H,EAAS8H,EAAQC,EAAQN,EAAMC,EAAMM,EAASC,EAAaC,EAAOC,EARhGC,GAAKhB,EAAI,IAAIiB,QAAQtJ,EAAa,SAAAuJ,OAAWC,GAAKD,SAAWC,EAAI,OAAe,KAALA,EAAe,EAAIA,IAAMpE,MAAMtF,IAAgB,GAC7HqC,EAAO,GACPsH,EAAY,EACZC,EAAY,EAEZC,EAAWN,EAAEhI,OACbiB,EAAS,EACTsH,EAAe,0BAA4BvB,MAOvCA,IAAMX,MAAM2B,EAAE,KAAO3B,MAAM2B,EAAE,WACjCQ,QAAQC,IAAIF,GACLzH,MAEHhB,EAAI,EAAGA,EAAIwI,EAAUxI,OACzB+H,EAAcL,EACVnB,MAAM2B,EAAElI,IAEX2H,GADAD,EAAUQ,EAAElI,GAAG4I,iBACWV,EAAElI,GAE5BA,IAEDa,GAAKqH,EAAElI,EAAI,GACXD,GAAKmI,EAAElI,EAAI,GACP2H,IACH9G,GAAKyH,EACLvI,GAAKwI,GAEDvI,IACJ4H,EAAS/G,EACTgH,EAAS9H,GAIM,MAAZ2H,EACC5H,IACCA,EAAQI,OAAS,IACpBc,EAAKd,OAELiB,GAAUrB,EAAQI,QAGpBoI,EAAYV,EAAS/G,EACrB0H,EAAYV,EAAS9H,EACrBD,EAAU,CAACe,EAAGd,GACdiB,EAAKiG,KAAKnH,GACVE,GAAK,EACL0H,EAAU,SAGJ,GAAgB,MAAZA,EAILC,IACJW,EAAYC,EAAY,IAHxBzI,EADIA,GACM,CAAC,EAAG,IAMPmH,KAAKpG,EAAGd,EAAGuI,EAAuB,EAAXJ,EAAElI,EAAI,GAAQuI,EAAuB,EAAXL,EAAElI,EAAI,GAASsI,GAAwB,EAAXJ,EAAElI,EAAI,GAAUuI,GAAwB,EAAXL,EAAElI,EAAI,IACxHA,GAAK,OAGC,GAAgB,MAAZ0H,EACVH,EAAOe,EACPd,EAAOe,EACa,MAAhBR,GAAuC,MAAhBA,IAC1BR,GAAQe,EAAYxI,EAAQA,EAAQI,OAAS,GAC7CsH,GAAQe,EAAYzI,EAAQA,EAAQI,OAAS,IAEzCyH,IACJW,EAAYC,EAAY,GAEzBzI,EAAQmH,KAAKM,EAAMC,EAAM3G,EAAGd,EAAIuI,GAAwB,EAAXJ,EAAElI,EAAI,GAAUuI,GAAwB,EAAXL,EAAElI,EAAI,IAChFA,GAAK,OAGC,GAAgB,MAAZ0H,EACVH,EAAOe,EA7EI,EAAI,GA6EKzH,EAAIyH,GACxBd,EAAOe,EA9EI,EAAI,GA8EKxI,EAAIwI,GACnBZ,IACJW,EAAYC,EAAY,GAEzBD,GAAwB,EAAXJ,EAAElI,EAAI,GACnBuI,GAAwB,EAAXL,EAAElI,EAAI,GACnBF,EAAQmH,KAAKM,EAAMC,EAAMc,EApFd,EAAI,GAoFuBzH,EAAIyH,GAAwBC,EApFvD,EAAI,GAoFgExI,EAAIwI,GAAwBD,EAAWC,GACtHvI,GAAK,OAGC,GAAgB,MAAZ0H,EACVH,EAAOe,EAAYxI,EAAQA,EAAQI,OAAS,GAC5CsH,EAAOe,EAAYzI,EAAQA,EAAQI,OAAS,GAC5CJ,EAAQmH,KAAKqB,EAAYf,EAAMgB,EAAYf,EAAM3G,EA3FtC,EAAI,GA2FwCyH,EAAmB,IAAPf,EAAc1G,GAAgBd,EA3FtF,EAAI,GA2FwFwI,EAAmB,IAAPf,EAAczH,GAAiBuI,EAAYzH,EAAK0H,EAAYxI,GAC/KC,GAAK,OAGC,GAAgB,MAAZ0H,EACVlH,GAAK8H,EAAWC,EAAYD,EAAYzH,EAAI0H,GAC5CvI,GAAK,OAGC,GAAgB,MAAZ0H,EAEVlH,GAAK8H,EAAWC,EAAWD,EAAYC,EAAY1H,GAAK8G,EAAaY,EAAYD,EAAY,IAC7FtI,GAAK,OAGC,GAAgB,MAAZ0H,GAA+B,MAAZA,EACb,MAAZA,IACH7G,EAAI+G,EACJ7H,EAAI8H,EACJ/H,EAAQ+I,QAAS,IAEF,MAAZnB,GAAyC,GAAtBpI,EAAKgJ,EAAYzH,IAAkC,GAAtBvB,EAAKiJ,EAAYxI,MACpES,GAAK8H,EAAWC,EAAW1H,EAAGd,GACd,MAAZ2H,IACH1H,GAAK,IAGPsI,EAAYzH,EACZ0H,EAAYxI,OAGN,GAAgB,MAAZ2H,EAAiB,IAC3BM,EAAQE,EAAElI,EAAE,GACZiI,EAAQC,EAAElI,EAAE,GACZuH,EAAOW,EAAElI,EAAE,GACXwH,EAAOU,EAAElI,EAAE,GACXyH,EAAI,EACe,EAAfO,EAAM9H,SACL8H,EAAM9H,OAAS,GAClBsH,EAAOD,EACPA,EAAOU,EACPR,MAEAD,EAAOS,EACPV,EAAOS,EAAMc,OAAO,GACpBrB,GAAG,GAEJQ,EAAQD,EAAMe,OAAO,GACrBf,EAAQA,EAAMe,OAAO,IAEtBjB,EAAUpD,aAAa4D,EAAWC,GAAYL,EAAElI,EAAE,IAAKkI,EAAElI,EAAE,IAAKkI,EAAElI,EAAE,IAAKgI,GAAQC,GAAQN,EAAaW,EAAY,GAAU,EAALf,GAASI,EAAaY,EAAY,GAAU,EAALf,GAC9JxH,GAAKyH,EACDK,MACEL,EAAI,EAAGA,EAAIK,EAAQ5H,OAAQuH,IAC/B3H,EAAQmH,KAAKa,EAAQL,IAGvBa,EAAYxI,EAAQA,EAAQI,OAAO,GACnCqI,EAAYzI,EAAQA,EAAQI,OAAO,QAGnCwI,QAAQC,IAAIF,UAGdzI,EAAIF,EAAQI,QACJ,GACPc,EAAKgI,MACLhJ,EAAI,GACMF,EAAQ,KAAOA,EAAQE,EAAE,IAAMF,EAAQ,KAAOA,EAAQE,EAAE,KAClEF,EAAQ+I,QAAS,GAElB7H,EAAKiI,YAAc9H,EAASnB,EACrBgB,EAmRD,SAASoD,gBAAgBsC,GAC3BhH,EAAUgH,EAAQ,MACrBA,EAAU,CAACA,QAIXwC,EAAIC,EAAGnJ,EAAGF,EAFPsJ,EAAS,GACZC,EAAI3C,EAAQxG,WAERiJ,EAAI,EAAGA,EAAIE,EAAGF,IAAK,KACvBrJ,EAAU4G,EAAQyC,GAClBC,GAAU,IAAMzJ,EAAOG,EAAQ,IAAM,IAAMH,EAAOG,EAAQ,IAAM,KAChEoJ,EAAKpJ,EAAQI,OACRF,EAAI,EAAGA,EAAIkJ,EAAIlJ,IACnBoJ,GAAUzJ,EAAOG,EAAQE,MAAQ,IAAML,EAAOG,EAAQE,MAAQ,IAAML,EAAOG,EAAQE,MAAQ,IAAML,EAAOG,EAAQE,MAAQ,IAAML,EAAOG,EAAQE,MAAQ,IAAML,EAAOG,EAAQE,IAAM,IAE7KF,EAAQ+I,SACXO,GAAU,YAGLA,ECxlCI,SAAXE,WAAiBC,GAA4B,oBAAZC,SAA4BD,EAAOC,OAAOD,OAASA,EAAKE,gBAAkBF,EAC7F,SAAdG,EAAchL,SAA2B,mBAAXA,EAavB,SAAPiL,EAAOC,UAAWlB,SAAWA,QAAQmB,KAAKD,GAE1B,SAAhBE,EAAgBhK,OAIdE,EAHGqJ,EAAIvJ,EAAQI,OACfW,EAAI,EACJd,EAAI,MAEAC,EAAI,EAAGA,EAAIqJ,EAAGrJ,IAClBa,GAAKf,EAAQE,KACbD,GAAKD,EAAQE,SAEP,CAACa,GAAKwI,EAAI,GAAItJ,GAAKsJ,EAAI,IAEpB,SAAXU,EAAWjK,OAMTe,EAAGd,EAAGC,EALHqJ,EAAIvJ,EAAQI,OACf8J,EAAOlK,EAAQ,GACfmK,EAAOD,EACPE,EAAOpK,EAAQ,GACfqK,EAAOD,MAEHlK,EAAI,EAAGA,EAAIqJ,EAAGrJ,GAAG,EAGbgK,GAFRnJ,EAAIf,EAAQE,IAGXgK,EAAOnJ,EACGA,EAAIoJ,IACdA,EAAOpJ,GAEAqJ,GANRnK,EAAID,EAAQE,EAAE,IAObkK,EAAOnK,EACGA,EAAIoK,IACdA,EAAOpK,UAGTD,EAAQsK,SAAWJ,EAAOC,GAAQ,EAClCnK,EAAQuK,SAAWH,EAAOC,GAAQ,EAC1BrK,EAAQwK,MAAQN,EAAOC,IAASC,EAAOC,GAEhC,SAAhBI,EAAiB7D,EAAS8D,YAAAA,IAAAA,EAAmB,WAO3CnB,EAAGxI,EAAGd,EAAGC,EAAGF,EAAS2K,EAAGC,EAAGC,EAAK5G,EAAIC,EAAI1C,EAAIC,EAAIC,EAAIG,EAAIC,EAAIC,EANzD4F,EAAIf,EAAQxG,OACf8J,EAAOtD,EAAQ,GAAG,GAClBuD,EAAOD,EACPE,EAAOxD,EAAQ,GAAG,GAClByD,EAAOD,EACPU,EAAM,EAAIJ,GAEG,IAAL/C,OAER4B,GADAvJ,EAAU4G,EAAQe,IACNvH,OACPF,EAAI,EAAGA,EAAIqJ,EAAGrJ,GAAG,MACrB+D,EAAKjE,EAAQE,GACbgE,EAAKlE,EAAQE,EAAE,GACfsB,EAAKxB,EAAQE,EAAE,GAAK+D,EACpBpC,EAAK7B,EAAQE,EAAE,GAAKgE,EACpBzC,EAAKzB,EAAQE,EAAE,GAAK+D,EACpBnC,EAAK9B,EAAQE,EAAE,GAAKgE,EACpBxC,EAAK1B,EAAQE,EAAE,GAAK+D,EACpBlC,EAAK/B,EAAQE,EAAE,GAAKgE,EACpByG,EAAID,GACU,IAALC,GAKAT,GAFRnJ,IAFA6J,EAAIE,EAAMH,GAEDC,EAAIlJ,EAAK,GADlBmJ,EAAM,EAAID,IACmBA,EAAInJ,EAAKoJ,EAAMrJ,IAAOoJ,EAAI3G,GAGtDiG,EAAOnJ,EACGA,EAAIoJ,IACdA,EAAOpJ,GAEAqJ,GANRnK,GAAK2K,EAAIA,EAAI7I,EAAK,EAAI8I,GAAOD,EAAI9I,EAAK+I,EAAMhJ,IAAO+I,EAAI1G,GAOtDkG,EAAOnK,EACGA,EAAIoK,IACdA,EAAOpK,UAKX2G,EAAQ0D,SAAWJ,EAAOC,GAAQ,EAClCvD,EAAQ2D,SAAWH,EAAOC,GAAQ,EAClCzD,EAAQmE,KAAOZ,EACfvD,EAAQhD,MAASsG,EAAOC,EACxBvD,EAAQoE,IAAMX,EACdzD,EAAQ/C,OAAUuG,EAAOC,EACjBzD,EAAQ4D,MAAQN,EAAOC,IAASC,EAAOC,GAE5B,SAApBY,EAAqB7C,EAAG8C,UAAMA,EAAE9K,OAASgI,EAAEhI,OAC7B,SAAd+K,EAAe/C,EAAG8C,OACbE,EAAQhD,EAAEoC,MAAQP,EAAS7B,GAC9BiD,EAAQH,EAAEV,MAAQP,EAASiB,UACpBhM,KAAKO,IAAI4L,EAAQD,IAAUA,EAAQC,GAAS,GAAOH,EAAEZ,QAAUlC,EAAEkC,SAAaY,EAAEX,QAAUnC,EAAEmC,QAAWc,EAAQD,EAEvG,SAAjBE,EAAkBtL,EAASuL,OAIzBrL,EAAGsL,EAHApD,EAAIpI,EAAQ8C,MAAM,GACrByG,EAAIvJ,EAAQI,OACZqL,EAAOlC,EAAI,MAEZgC,GAA0B,EACrBrL,EAAI,EAAGA,EAAIqJ,EAAGrJ,IAClBsL,GAAStL,EAAIqL,GAAcE,EAC3BzL,EAAQE,KAAOkI,EAAEoD,GACjBxL,EAAQE,GAAKkI,EAAQ,EAANoD,GAGG,SAApBE,EAAqBC,EAAIC,EAAIL,EAAYM,EAASC,OAIhDN,EAAOtL,EAAGa,EAAGd,EAHVsJ,EAAIoC,EAAGvL,OACVgH,EAAI,EACJqE,EAAOlC,EAAI,MAEZgC,GAAc,EACTrL,EAAI,EAAGA,EAAIqJ,EAAGrJ,GAAK,EAEvBD,EAAI0L,EADJH,GAAStL,EAAIqL,GAAcE,IACVG,EAAG1L,GAAK2L,GACzB9K,EAAI4K,EAAS,EAANH,IAAYI,EAAG1L,EAAE,GAAK4L,GAC7B1E,GAAK1H,EAAMqB,EAAIA,EAAId,EAAIA,UAEjBmH,EAEgB,SAAxB2E,EAAyBJ,EAAIC,EAAII,OAQ/BC,EAAM7E,EAAGlH,EAPNqJ,EAAIoC,EAAGvL,OACV8L,EAAUlC,EAAc2B,GACxBQ,EAAUnC,EAAc4B,GACxBC,EAAUM,EAAQ,GAAKD,EAAQ,GAC/BJ,EAAUK,EAAQ,GAAKD,EAAQ,GAC/BE,EAAMV,EAAkBC,EAAIC,EAAI,EAAGC,EAASC,GAC5CO,EAAW,MAEPnM,EAAI,EAAGA,EAAIqJ,EAAGrJ,GAAK,GACvBkH,EAAIsE,EAAkBC,EAAIC,EAAI1L,EAAI,EAAG2L,EAASC,IACtCM,IACPA,EAAMhF,EACNiF,EAAWnM,MAGT8L,MAEHjM,eADAkM,EAAON,EAAG7I,MAAM,IAEX5C,EAAI,EAAGA,EAAIqJ,EAAGrJ,GAAK,GACvBkH,EAAIsE,EAAkBO,EAAML,EAAI1L,EAAI,EAAG2L,EAASC,IACxCM,IACPA,EAAMhF,EACNiF,GAAYnM,UAIRmM,EAAW,EAEC,SAApBC,EAAqB1F,EAAS7F,EAAGd,WAK/BD,EAASuM,EAAIC,EAAIpF,EAAGlH,EAAGqJ,EAJpB5B,EAAIf,EAAQxG,OACfqM,EAxJQ,KAyJRC,EAAW,EACXC,EAAW,GAEE,IAALhF,OAER4B,GADAvJ,EAAU4G,EAAQe,IACNvH,OACPF,EAAI,EAAGA,EAAIqJ,EAAGrJ,GAAK,EACvBqM,EAAKvM,EAAQE,GAAKa,EAClByL,EAAKxM,EAAQE,EAAE,GAAKD,GACpBmH,EAAI1H,EAAM6M,EAAKA,EAAKC,EAAKA,IACjBC,IACPA,EAAkBrF,EAClBsF,EAAW1M,EAAQE,GACnByM,EAAW3M,EAAQE,EAAE,UAIjB,CAACwM,EAAUC,GAEE,SAArBC,EAAsBC,EAAQC,EAAMC,EAAYC,EAAWnB,EAASC,OAO5D5L,EAAGqM,EAAIC,EAAIpF,EANdmC,EAAIuD,EAAK1M,OACZoL,EAAQ,EACRyB,EAAU/N,KAAKkN,IAAIS,EAAOrC,MAAQP,EAAS4C,GAASC,EAAKC,GAAYvC,MAAQP,EAAS6C,EAAKC,KAAgBC,EAC3GZ,EAhLQ,KAiLRrI,EAAK8I,EAAOvC,QAAUuB,EACtB7H,EAAK6I,EAAOtC,QAAUuB,MAElB5L,EAAI6M,EAAY7M,EAAIqJ,MACjBuD,EAAK5M,GAAGsK,MAAQP,EAAS6C,EAAK5M,KAC1B+M,GAFgB/M,IAK3BqM,EAAKO,EAAK5M,GAAGoK,QAAUvG,EACvByI,EAAKM,EAAK5M,GAAGqK,QAAUvG,GACvBoD,EAAI1H,EAAM6M,EAAKA,EAAKC,EAAKA,IACjBJ,IACPZ,EAAQtL,EACRkM,EAAMhF,UAGRA,EAAI0F,EAAKtB,GACTsB,EAAKI,OAAO1B,EAAO,GACZpE,EAEe,SAAvB+F,EAAwBnN,EAASoN,OAK/BC,EAAIC,EAAIC,EAAMC,EAAMC,EAAMC,EAAMC,EAAIC,EACpC3J,EAAIC,EAAI1C,EAAIK,EAAI3B,EAAG0K,EALhBiD,EAAQ,EAEXtE,EAAIvJ,EAAQI,OACZ0N,EAAsBV,IAAa7D,EAAI,GAAK,OAGxCrJ,EAAI,EAAGA,EAAIqJ,EAAGrJ,GAAK,MACvB2N,GAASC,EANH,QAOCD,GACNR,EAAKrN,EAAQE,EAAE,GACfoN,EAAKtN,EAAQE,EAAE,GACfqN,EAAOvN,EAAQE,GACfsN,EAAOxN,EAAQE,EAAE,GACjBuN,EAAOzN,EAAQE,EAAE,GACjBwN,EAAO1N,EAAQE,EAAE,GACjByN,EAAK3N,EAAQE,EAAE,GACf0N,EAAK5N,EAAQE,EAAE,GAEf+D,EAAKoJ,GAAME,EAAOF,IADlBzC,EAAI,IAAM1L,KAAK6O,MAAMF,IAAU,GAAK,IAGpC5J,KADAzC,EAAK+L,GAAQE,EAAOF,GAAQ3C,GAChB3G,GAAM2G,EAClBpJ,IAAQiM,GAAQE,EAAKF,GAAQ7C,EAAKpJ,GAAMoJ,EACxC1G,EAAKoJ,GAAME,EAAOF,GAAM1C,EAExB1G,KADArC,EAAK2L,GAAQE,EAAOF,GAAQ5C,GAChB1G,GAAM0G,EAClB/I,IAAQ6L,GAAQE,EAAKF,GAAQ9C,EAAK/I,GAAM+I,EACxC5K,EAAQkN,OAAOhN,EAAG,EACjBmN,GAAME,EAAOF,GAAMzC,EACnB0C,GAAME,EAAOF,GAAM1C,EACnB3G,EACAC,EACAD,GAAMzC,EAAKyC,GAAM2G,EACjB1G,GAAMrC,EAAKqC,GAAM0G,EACjBpJ,EACAK,EACA4L,GAAQE,EAAKF,GAAQ7C,EACrB8C,GAAQE,EAAKF,GAAQ9C,GAEtB1K,GAAK,EACLqJ,GAAK,EACLsE,WAGK7N,EAEmB,SAA3BgO,EAA4BC,EAAOC,EAAK3C,EAAY4C,EAAKC,OAWvDxC,EAAID,EAAIT,EAAGnK,EAAGd,EAAG4L,EAASC,EAVvBuC,EAAMH,EAAI9N,OAAS6N,EAAM7N,OAC5BkO,EAAe,EAAND,EAAUH,EAAMD,EACzBM,EAAgB,EAANF,EAAUJ,EAAQC,EAC5BM,EAAQ,EACRC,EAAsB,eAARN,EAAwBlD,EAAoBE,EAC1D6B,EAAqB,aAARmB,EAAsB,EAAqB,iBAATA,EAAqBA,EAAM,GAC1EjO,EAAIqO,EAAQnO,OACZsO,EAAuC,iBAAhBnD,GAA4BA,EAAWpE,KAAQoE,EAAWzI,MAAM,GAAK,CAACyI,GAC7FpL,EAA+B,YAApBuO,EAAa,IAAoBA,EAAa,GAAK,EAC9D7F,EAAsB,QAAf0C,KAEHgD,EAAQ,OAGO,EAAhBD,EAAOlO,SACV6N,EAAMU,KAAKF,GACXP,EAAIS,KAAKF,GACCH,EAAO9D,MAAQC,EAAc6D,GAC7BC,EAAQ/D,MAAQC,EAAc8D,GACxC1C,EAAUyC,EAAOhE,QAAUiE,EAAQjE,QACnCwB,EAAUwC,EAAO/D,QAAUgE,EAAQhE,QAC/BkE,IAAetD,OACbjL,EAAI,EAAGA,EAAIqO,EAAQnO,OAAQF,IAC/BoO,EAAOpB,OAAOhN,EAAG,EAAG0M,EAAmB2B,EAAQrO,GAAIoO,EAAQpO,EAAG8M,EAAWnB,EAASC,OAIjFuC,MACCA,EAAM,IACTA,GAAOA,GAEJC,EAAO,GAAGlO,OAASmO,EAAQ,GAAGnO,QACjC+M,EAAqBoB,EAAQ,IAAMD,EAAO,GAAGlO,OAASmO,EAAQ,GAAGnO,QAAQ,EAAK,GAE/EF,EAAIqO,EAAQnO,OACLoO,EAAQH,GACVC,EAAOpO,GAAGsK,MAAQP,EAASqE,EAAOpO,IAEtCa,GADAmK,EAAIoB,EAAkBiC,EAASD,EAAOpO,GAAGoK,QAASgE,EAAOpO,GAAGqK,UACtD,GACNtK,EAAIiL,EAAE,GACNqD,EAAQrO,KAAO,CAACa,EAAGd,EAAGc,EAAGd,EAAGc,EAAGd,EAAGc,EAAGd,GACrCsO,EAAQpF,aAAe,EACvBqF,QAGGtO,EAAI,EAAGA,EAAI+N,EAAM7N,OAAQF,IAC7B0L,EAAKsC,EAAIhO,GACTyL,EAAKsC,EAAM/N,IACXmO,EAAMzC,EAAGxL,OAASuL,EAAGvL,QACX,EACT+M,EAAqBvB,GAAMyC,EAAI,EAAK,GACpB,EAANA,GACVlB,EAAqBxB,EAAK0C,EAAI,EAAK,GAEhClO,IAAwB,IAAbiO,IAAuBzC,EAAGtL,UACxCN,eAAe4L,IAEhBJ,EAAcmD,EAAaxO,IAA0B,IAApBwO,EAAaxO,GAAYwO,EAAaxO,GAAK,UAGvEyL,EAAG5C,QAAW7J,KAAKO,IAAIkM,EAAG,GAAKA,EAAGA,EAAGvL,OAAS,IAAM,IAAOlB,KAAKO,IAAIkM,EAAG,GAAKA,EAAGA,EAAGvL,OAAS,IAAM,GACjF,SAAfmL,GAAwC,QAAfA,GAC5BmD,EAAaxO,GAAKqL,EAAaQ,EAAsBJ,EAAIC,GAAM1L,IAAkB,IAAbkO,GAChE7C,EAAa,IAChBpL,GAAU,EACVJ,eAAe4L,GACfJ,GAAcA,GAEfD,EAAeK,EAAiB,EAAbJ,IAEM,YAAfA,IACNrL,GAAKqL,EAAa,GACrBxL,eAAe4L,GAEhBL,EAAeK,EAAkD,GAA7CJ,EAAa,GAAKA,EAAaA,MAGzCpL,IAA2B,SAAfoL,GAA0BrM,KAAKO,IAAImM,EAAG,GAAKD,EAAG,IAAMzM,KAAKO,IAAImM,EAAG,GAAKD,EAAG,IAAMzM,KAAKO,IAAImM,EAAGA,EAAGxL,OAAS,GAAKuL,EAAGA,EAAGvL,OAAS,IAAMlB,KAAKO,IAAImM,EAAGA,EAAGxL,OAAS,GAAKuL,EAAGA,EAAGvL,OAAS,IAAMlB,KAAKO,IAAImM,EAAG,GAAKD,EAAGA,EAAGvL,OAAS,IAAMlB,KAAKO,IAAImM,EAAG,GAAKD,EAAGA,EAAGvL,OAAS,IAAMlB,KAAKO,IAAImM,EAAGA,EAAGxL,OAAS,GAAKuL,EAAG,IAAMzM,KAAKO,IAAImM,EAAGA,EAAGxL,OAAS,GAAKuL,EAAG,KAASJ,EAAa,IACjXxL,eAAe4L,GACf+C,EAAaxO,IAAM,EACnBC,GAAU,GACe,SAAfoL,EACVmD,EAAaxO,GAAK,EACO,YAAfqL,IACVmD,EAAaxO,IAAM,GAEhByL,EAAG5C,SAAW6C,EAAG7C,SACpB4C,EAAG5C,OAAS6C,EAAG7C,QAAS,WAI3BF,GAAOgB,EAAK,eAAiB6E,EAAa5K,KAAK,KAAO,KACtDmK,EAAM1C,WAAamD,GAGN,SAAdE,EAAexG,EAAGmD,EAAY4C,EAAKU,EAAYT,OAC1CH,EAAQzJ,gBAAgB4D,EAAE,IAC7B8F,EAAM1J,gBAAgB4D,EAAE,IACpB4F,EAAyBC,EAAOC,EAAM3C,GAA6B,IAAfA,EAAoBA,EAAa,OAAQ4C,EAAKC,KAGvGhG,EAAE,GAAK9D,gBAAgB2J,GACvB7F,EAAE,GAAK9D,gBAAgB4J,GACJ,QAAfW,IAAuC,IAAfA,GAC3BhF,EAAK,gBAAkBzB,EAAE,GAAK,MAAQA,EAAE,GAAK,OAyBtB,SAAzB0G,GAA0B1G,EAAGgF,OAM3BU,EAAqB5N,EAAGqJ,EAAG5B,EAAGoH,EAAQC,EAAOC,EAL1CpB,EAAQ,EACX9M,EAAImO,WAAW9G,EAAE,IACjBnI,EAAIiP,WAAW9G,EAAE,IACjBiB,EAAItI,EAAI,IAAMd,EAAI,QAInB6N,EAAiC,GAAXV,GAAsB,IAD5C7D,EAAInB,EAAEhI,QAC4C,GAC7CF,EAAI,EAAGA,EAAIqJ,EAAE,EAAGrJ,GAAK,EAAG,IAC5B2N,GAASC,EACTkB,EAAQE,WAAW9G,EAAElI,EAAE,IACvB+O,EAAQC,WAAW9G,EAAElI,EAAE,IAPjB,QAQF2N,MACHkB,EAAS,GAAK7P,KAAK6O,MAAMF,GAAS,GAClClG,EAAI,EAVC,QAWEkG,GACNxE,IAAMtI,GAAKiO,EAAQjO,GAAKgO,EAASpH,GAAGwH,QAAQ,GAAK,KAAOlP,GAAKgP,EAAQhP,GAAK8O,EAASpH,GAAGwH,QAAQ,GAAK,IACnGtB,IACAlG,IAGF0B,GAAK2F,EAAQ,IAAMC,EAAQ,IAC3BlO,EAAIiO,EACJ/O,EAAIgP,SAEE5F,EAEQ,SAAhB+F,GAAgBhH,OACXiH,EAAYjH,EAAE,GAAGjE,MAAMmL,IAAY,GACtCC,EAAUnH,EAAE,GAAGjE,MAAMmL,IAAY,GACjCjB,EAAMkB,EAAQnP,OAASiP,EAAUjP,OACxB,EAANiO,EACHjG,EAAE,GAAK0G,GAAuBO,EAAWhB,GAEzCjG,EAAE,GAAK0G,GAAuBS,GAAUlB,GAGrB,SAArBmB,GAAqBjE,UAAe9E,MAAM8E,GAGrC6D,GAHmD,SAAAhH,GACtDgH,GAAchH,GACdA,EAAE,GA9DY,SAAhBqH,cAAiBC,EAAMC,OACjBA,SACGD,MAKP5E,EAAK5K,EAAGyH,EAHLS,EAAIsH,EAAKvL,MAAMmL,IAAY,GAC9B/F,EAAInB,EAAEhI,OACNiJ,EAAI,OAIJyB,EAFc,YAAX6E,GACHzP,EAAIqJ,EAAE,GACC,IAEPrJ,GAAoC,GAA7B0P,SAASD,EAAQ,KAAO,GAAS,EAAS,IAAJpG,GAAWA,EAClD,GAEF5B,EAAI,EAAGA,EAAI4B,EAAG5B,GAAK,EACvB0B,GAAKjB,EAAElI,EAAE,GAAK,IAAMkI,EAAElI,GAAK,IAC3BA,GAAKA,EAAI4K,GAAOvB,SAEVF,EA2CCoG,CAAcrH,EAAE,GAAIwH,SAASrE,EAAY,MAyB5B,SAAtBsE,GAAuBjJ,EAASkJ,WAG9BC,EAAQ/P,EAASe,EAAGd,EAAGuB,EAAIK,EAAI3B,EAAGqJ,EAAGnB,EAAG4H,EAAIC,EAAUC,EAFnDvI,EAAIf,EAAQxG,OACf+P,EAAQ,IAAOL,GAAa,IAEf,IAALnI,GAAQ,KAEhBsI,GADAjQ,EAAU4G,EAAQe,IACCsI,SAAWjQ,EAAQiQ,UAAY,CAAC,EAAG,EAAG,EAAG,GAC5DC,EAAalQ,EAAQkQ,WAAalQ,EAAQkQ,YAAc,CAAC,EAAG,EAAG,EAAG,GAClED,EAAS7P,OAAS,EAClBmJ,EAAIvJ,EAAQI,OAAS,EAChBF,EAAI,EAAGA,EAAIqJ,EAAGrJ,GAAK,EACvBa,EAAIf,EAAQE,GAAKF,EAAQE,EAAI,GAC7BD,EAAID,EAAQE,EAAI,GAAKF,EAAQE,EAAI,GACjCsB,EAAKxB,EAAQE,EAAI,GAAKF,EAAQE,GAC9B2B,EAAK7B,EAAQE,EAAI,GAAKF,EAAQE,EAAI,GAClCkI,EAAIgI,EAAOnQ,EAAGc,GACdiP,EAAKI,EAAOvO,EAAIL,IAChBuO,EAAU7Q,KAAKO,IAAI2I,EAAI4H,GAAMG,KAE5BD,EAAWhQ,EAAI,GAAKkI,EACpB8H,EAAWhQ,EAAI,GAAK8P,EACpBE,EAAWhQ,EAAI,GAAKR,EAAMqB,EAAIA,EAAId,EAAIA,GACtCiQ,EAAWhQ,EAAI,GAAKR,EAAM8B,EAAKA,EAAKK,EAAKA,IAE1CoO,EAAS9I,KAAK4I,EAAQA,EAAQ,EAAG,EAAGA,EAAQA,GAGzC/P,EAAQuJ,KAAOvJ,EAAQ,IAAMA,EAAU,EAAFuJ,KAASvJ,EAAQ,KACzDe,EAAIf,EAAQ,GAAKA,EAAQuJ,EAAE,GAC3BtJ,EAAID,EAAQ,GAAKA,EAAQuJ,EAAE,GAC3B/H,EAAKxB,EAAQ,GAAKA,EAAQ,GAC1B6B,EAAK7B,EAAQ,GAAKA,EAAQ,GAC1BoI,EAAIgI,EAAOnQ,EAAGc,GACdiP,EAAKI,EAAOvO,EAAIL,GACZtC,KAAKO,IAAI2I,EAAI4H,GAAMG,IACtBD,EAAW3G,EAAE,GAAKnB,EAClB8H,EAAW,GAAKF,EAChBE,EAAW3G,EAAE,GAAK7J,EAAMqB,EAAIA,EAAId,EAAIA,GACpCiQ,EAAW,GAAKxQ,EAAM8B,EAAKA,EAAKK,EAAKA,GACrCoO,EAAS1G,EAAE,GAAK0G,EAAS1G,EAAE,IAAK,WAI5B3C,EAEc,SAAtByJ,GAAsBC,OACjBlI,EAAIkI,EAAEC,OAAO/M,MAAM,WAGhB,CAACzC,IAFFuP,EAAEpN,QAAQ,QAAU,GAAKoN,EAAEpN,QAAQ,SAAW,IAAMuD,MAAMyI,WAAW9G,EAAE,KAAO,GAAK8G,WAAW9G,EAAE,KAExF,IAAKnI,IADbqQ,EAAEpN,QAAQ,OAAS,GAAKoN,EAAEpN,QAAQ,UAAY,IAAMuD,MAAMyI,WAAW9G,EAAE,KAAO,GAAK8G,WAAW9G,EAAE,KAC7E,KAIT,SAAjBoI,GAA0BvC,EAAOC,EAAKhO,EAAGuQ,OAOvCC,EAAUC,EANPC,EAAKC,KAAKC,QACbC,EAAKF,KAAKG,SACVzE,EAAK0B,EAAM/N,GAAK0Q,EAAG7P,EACnByL,EAAKyB,EAAM/N,EAAE,GAAK0Q,EAAG3Q,EACrBmH,EAAI1H,EAAM6M,EAAKA,EAAKC,EAAKA,GACzByE,EAAKb,EAAO5D,EAAID,UAEjBA,EAAK2B,EAAIhO,GAAK6Q,EAAGhQ,EACjByL,EAAK0B,EAAIhO,EAAE,GAAK6Q,EAAG9Q,EAEnB0Q,EAba,SAAdO,YAAc7C,UAAQA,IAAQA,EAAM8C,EAAO9C,GAAQA,EAAM,EAAK+C,GAAQA,GAAQ/C,EAarE6C,CADRR,EAAWN,EAAO5D,EAAID,GAAM0E,IAGvBR,GAAYY,GAAqBnS,KAAKO,IAAIkR,EAAQU,EAAkBC,IAAMC,IAC9Ed,EAAWY,GAEJR,KAAKW,UAAYH,EAAoB,CAC5CI,MAAMZ,KAAKW,UACX5G,EAAEqD,EACFgD,GAAGA,EACHK,GAAIb,GAAYE,EAAQF,EAASa,GAAK,GAAKpS,KAAKO,IAAIkR,GAASe,EAAahB,EAAWC,EACrFvH,GAAGhC,EACHuK,GAAGjS,EAAM6M,EAAKA,EAAKC,EAAKA,GAAMpF,EAC9BlH,EAAEA,GAGQ,SAAZ0R,GAAYC,GACXpI,EAAOD,IACPsI,EAAcA,GAAgBrI,GAAQA,EAAKsI,QAAQC,SAC/CvI,GAAQqI,GACXG,EAAWxI,EAAKyI,MAAMC,QAEtBL,EAAYM,UAAU5B,eAAiBA,GACvC6B,EAAe,GACLR,GACVhI,EAAK,kDAzhBJJ,EAAMwI,EAAUZ,EAAyBgB,EAAcP,EAG1D1B,EAASlR,KAAKoT,MACdhT,EAAOJ,KAAKK,IACZH,EAAOF,KAAKG,IACZK,EAAQR,KAAKS,KACbwR,EAAMjS,KAAKC,GACXiS,EAAa,EAAND,EACPI,EAAkB,GAANJ,EACZO,EAAkB,GAANP,EAEZ7B,EAAU,wCACVtQ,EAAe,4BACfuT,EAAY,gBAueZC,EAAgB,2EAyCJC,GAAiB,CAC7BC,QAAS,SACT/P,KAAM,WACNgQ,QAAS,EACTC,2BAASC,EAAMC,GACdrJ,EAAOoJ,EACPf,EAAcgB,EACdlB,MAEDmB,mBAAKC,EAAQpU,EAAOqU,EAAOzH,EAAO0H,MACjCb,GAAgBT,GAAU,IACrBhT,SACJiL,EAAK,kBACE,MAGJ1H,EAAMgR,EAAGC,EAAIC,EAAOC,EAAQ/H,EAAY4C,EAAKoF,EAAaC,EAAWvF,EAAOC,EAAKhO,EAAGyH,EAAG4B,EAAGkK,EAAUC,EAAQC,EAAaC,EAAOC,EAAOC,EAAeC,EAAapE,KADvK/F,EAAYhL,KAAWA,EAAQA,EAAMmE,KAAKkQ,EAAOzH,EAAOwH,EAAQE,IAE1C,iBAAXtU,GAAuBA,EAAM2D,SAAW3D,EAAM,GACxDA,EAAQ,CAACyU,MAAMzU,QACT,GAAsB,iBAAXA,EAAqB,KAEjCuU,KADLhR,EAAO,GACGvD,EACTuD,EAAKgR,GAAKvJ,EAAYhL,EAAMuU,KAAa,WAANA,EAAiBvU,EAAMuU,GAAGpQ,KAAKkQ,EAAOzH,EAAOwH,EAAQE,GAAWtU,EAAMuU,GAE1GvU,EAAQuD,MAEL6R,EAAKhB,EAAOiB,SAAWvK,OAAOwK,iBAAiBlB,GAAU,GAC5DmB,EAAOH,EAAGG,KAAO,GACjB/F,IAAsB,SAAT+F,GAAsD,OAAlCA,EAAKhQ,MAAMmL,IAAY,IAAI,IAA8B,YAAhB0E,EAAGI,UAC7EC,GAAWzV,EAAM0V,QAAU,SAAS9Q,MAAM,QAE3C8P,EAAmB,cADnBnR,GAAQ6Q,EAAO/P,SAAW,IAAI6F,gBACY,YAAT3G,EACpB,SAATA,IAAoBmR,IAAW1U,EAAM2V,YACxC1K,EAAK,mBAAqB1H,EAAO,cAAgBqQ,IAC1C,KAERW,EAAc,SAAThR,EAAmB,IAAM,UACzBvD,EAAM2V,OAAS3K,EAAYoJ,EAAO3O,qBAC/B,KAERgP,EA5Ja,SAAdmB,YAAenB,EAAOoB,EAAWzB,OAE/BvQ,EAAGN,WAD6B,iBAAXkR,IAELrU,EAAa0V,KAAKrB,KAAWA,EAAMlP,MAAMmL,IAAY,IAAIlP,OAAS,MAClFqC,EAAIwP,EAASoB,GAAO,KAEnBlR,GAAQM,EAAEQ,SAAW,IAAI6F,cACrB2L,GAAsB,SAATtS,IAChBM,EAAI9B,cAAc8B,GAAG,GACrBN,EAAO,QAERkR,EAAQ5Q,EAAEiB,aAAsB,SAATvB,EAAkB,IAAM,WAAa,GACxDM,IAAMuQ,IACTK,EAAQ5Q,EAAEkS,eAAe,KAAM,kBAAoBtB,KAGpDxJ,EAAK,8BAAgCwJ,GACrCA,GAAQ,IAGHA,EAwICmB,CAAY5V,EAAMyU,OAASzU,EAAMwI,GAAKxI,EAAMyC,QAAU,GAAW,MAAN8R,EAAYH,GAC3EM,GAAUf,EAAUmC,KAAKrB,UAC5BxJ,EAAK,MAAQ1H,EAAO,8BAAgCqQ,IAC7C,KAERjH,EAAc3M,EAAM2M,YAAmC,IAArB3M,EAAM2M,WAAoB3M,EAAM2M,WAAa,OAC/E4C,EAAMvP,EAAMuP,KAAOsE,GAAemC,gBAC7BC,MAAQjW,EAAM2V,UACdO,QAAUlW,EAAMmW,QAAUtC,GAAeuC,mBACzCC,OAAU,iBAAkBrW,EAASA,EAAMsW,aAAezC,GAAe0C,yBACzEC,KAAOlW,KAAKmW,IAAI,GAAI5O,MAAM7H,EAAM0W,WAAa,GAAK1W,EAAM0W,gBACxDC,OAAStC,EACVI,EAAO,SACLmC,QAAUxC,EACfW,EAA4C,iBAAtB/U,EAAMiQ,WAC5BZ,EAAQ4C,KAAKgE,MAAQ7B,EAAOnC,KAAKgE,OAAS7B,EAAOtP,aAAayP,GACzDtC,KAAKgE,OAAU7B,EAAO2B,eAAe,KAAM,kBAC/C3B,EAAO7P,eAAe,KAAM,gBAAiB8K,GAEpC,MAANkF,GAAatC,KAAKgE,MAAO,IAC5B5G,EAAQzJ,gBAAgBmP,EAAc/U,EAAMiQ,WAAW,GAAKZ,GAC5DC,EAAM1J,gBAAgBmP,EAAc/U,EAAMiQ,WAAW,GAAKwE,IACrDM,IAAgB3F,EAAyBC,EAAOC,EAAK3C,EAAY4C,EAAKC,UACnE,MAEiB,QAArBxP,EAAMiQ,aAA6C,IAArBjQ,EAAMiQ,YACvChF,EAAK,gBAAkBvF,gBAAgB2J,GAAS,MAAQ3J,gBAAgB4J,GAAO,OAEhF6F,EAA6D,YAA9CnV,EAAMuD,MAAQsQ,GAAegD,gBAE3CxH,EAAQ4B,GAAoB5B,EAAOrP,EAAM8W,iBACzCxH,EAAM2B,GAAoB3B,EAAKtP,EAAM8W,iBAChCzH,EAAMzD,MACVC,EAAcwD,GAEVC,EAAI1D,MACRC,EAAcyD,GAEf4F,EAAgBzD,GAAoBgE,EAAQ,SACvCvD,QAAU7C,EAAMqG,OAAS,CAACvT,EAAEkN,EAAMlD,KAAO+I,EAAc/S,EAAIkN,EAAMrK,MAAO3D,EAAEgO,EAAMjD,IAAM8I,EAAc7T,EAAIgO,EAAMpK,QAC/GwQ,EAAQ,KACXP,EAAgBzD,GAAoBgE,EAAQ,UAExCrD,SAAW,CAACjQ,EAAEmN,EAAInD,KAAO+I,EAAc/S,EAAImN,EAAItK,MAAO3D,EAAEiO,EAAIlD,IAAM8I,EAAc7T,EAAIiO,EAAIrK,cAGzF8R,SAAW3C,EAAOzO,WAAc0J,EAErCtG,EAAIsG,EAAM7N,QACI,IAALuH,OACR8L,EAAWxF,EAAMtG,GACjB+L,EAASxF,EAAIvG,GACb4L,EAAcE,EAASxD,UAAY,GACnCuD,EAAYE,EAAOzD,UAAY,GAC/B1G,EAAIkK,EAASrT,OAERF,EADLmR,EAAoB,EACRnR,EAAIqJ,EAAGrJ,GAAG,EACjBwT,EAAOxT,KAAOuT,EAASvT,IAAMwT,EAAOxT,EAAE,KAAOuT,EAASvT,EAAE,KACvD6T,EACCR,EAAYrT,IAAMsT,EAAUtT,IAC/B0T,EAAQH,EAASvD,WACjB2D,EAAQH,EAAOxD,WACfP,EAASzP,GAAMA,IAAMqJ,EAAI,EAAK,EAAIA,EAAI,QACjCqM,WAAa,CAACnE,MAAMZ,KAAK+E,WAAY1V,EAAEA,EAAGyH,EAAEA,EAAGkO,IAAIjC,EAAM1T,EAAE,GAAI4V,IAAIjC,EAAM3T,EAAE,GAAK0T,EAAM1T,EAAE,GAAI6V,IAAInC,EAAMjE,GAASqG,IAAInC,EAAMlE,GAAUiE,EAAMjE,IAC9IyD,EAAKvC,KAAKL,eAAeiD,EAAUC,EAAQxT,EAAE,QACxCsQ,eAAeiD,EAAUC,EAAQxT,EAAGkT,QACpC5C,eAAeiD,EAAUC,EAAQ/D,EAAO,EAAGyD,GAChDlT,GAAG,QAEEsQ,eAAeiD,EAAUC,EAAQxT,IAGvCkT,EAAKvC,KAAKoF,IAAIxC,EAAUvT,EAAGuT,EAASvT,GAAIwT,EAAOxT,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,GAClEkT,EAAKvC,KAAKoF,IAAIxC,EAAUvT,EAAE,EAAGuT,EAASvT,EAAE,GAAIwT,EAAOxT,EAAE,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,IAAMkT,SAMlFA,EAAKvC,KAAKoF,IAAIjD,EAAQ,eAAgBA,EAAOtP,aAAayP,GAAK,GAAIE,EAAQ,GAAI7H,EAAO0H,EAAS,EAAG1D,GAAmBjE,GAAa4H,GAG/HY,SACEkC,IAAIpF,KAAKC,QAAS,IAAKD,KAAKC,QAAQ/P,EAAG8P,KAAKG,SAASjQ,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC5EqS,EAAKvC,KAAKoF,IAAIpF,KAAKC,QAAS,IAAKD,KAAKC,QAAQ7Q,EAAG4Q,KAAKG,SAAS/Q,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,IAG9EmT,SACE8C,OAAO/O,KAAK,YACjBiM,EAAGlF,IAAMmF,EACTD,EAAG+C,QAAUhD,UAhpBE,GAspBlB4B,uBAAOqB,EAAOtV,WAOZuI,EAAUgN,EAAWrW,EAASuJ,EAAGxE,EAAO7E,EAAGyH,EAAG5G,EAAGd,EAAGZ,EAAKE,EAAKoQ,EAN3D/I,EAAU9F,EAAK6U,SAClBW,EAAYxV,EAAK8U,WACjBW,EAAWzV,EAAK0Q,UAChBgF,EAAM1V,EAAKsU,KACXpC,EAASlS,EAAK0U,QACdpC,EAAKtS,EAAK2V,IAEJrD,GACNA,EAAGpS,EAAEoV,EAAOhD,EAAGhM,GACfgM,EAAKA,EAAG3B,SAEK,IAAV2E,GAAetV,EAAKmU,WACvB7B,EAAKtS,EAAK2V,IACHrD,GACFA,EAAGlF,MACFpN,EAAK+T,MACR7B,EAAOlS,EAAK+T,OAASzB,EAAGlF,IAExB8E,EAAO3O,aAAa+O,EAAG+C,QAAS/C,EAAGlF,MAGrCkF,EAAKA,EAAG3B,WAEH,GAAI7K,EAAS,MAGZ2P,GACNxR,EAAQwR,EAAStF,GAAKmF,EAAQG,EAASjF,GACvC/H,EAAIgN,EAASnN,GAAKgN,EAAQG,EAAS5E,GACnC4E,EAAS3L,EAAE2L,EAASrW,GAAKY,EAAKgQ,QAAQ/P,EAAIzB,EAAKyF,GAASwE,EACxDgN,EAAS3L,EAAE2L,EAASrW,EAAI,GAAKY,EAAKgQ,QAAQ7Q,EAAIb,EAAK2F,GAASwE,EAC5DgN,EAAWA,EAAS9E,UAIrB4E,EAAYD,EAAQ,GAAM,EAAIA,EAAQA,GAAS,EAAI,EAAIA,GAASA,EAAQ,EACjEE,GAGN3G,GAFAzP,EAAIoW,EAAUpW,IAECA,KADfF,EAAU4G,EAAQ0P,EAAU3O,IACCvH,OAAS,EAAK,EAAIJ,EAAQI,OAAS,GAChE2E,EAAQqL,EAAOpQ,EAAQ2P,GAAU3P,EAAQE,EAAE,GAAIF,EAAQ2P,EAAO,GAAK3P,EAAQE,IAC3Eb,EAAMD,EAAK2F,GACXxF,EAAMD,EAAKyF,GACXhE,EAAIf,EAAQE,EAAE,GACdD,EAAID,EAAQE,EAAE,GACdqJ,EAAI+M,EAAUT,IAAMQ,EAAYC,EAAUR,IAC1C9V,EAAQE,GAAKa,EAAIxB,EAAMgK,EACvBvJ,EAAQE,EAAE,GAAKD,EAAIZ,EAAMkK,EACzBA,EAAI+M,EAAUP,IAAMM,EAAYC,EAAUN,IAC1ChW,EAAQ2P,EAAO,GAAK5O,EAAIxB,EAAMgK,EAC9BvJ,EAAQ2P,GAAU1P,EAAIZ,EAAMkK,EAC5B+M,EAAYA,EAAU7E,SAGvBuB,EAAOzO,WAAaqC,EAEhB9F,EAAKmU,OAAQ,KAChB5L,EAAI,GAEC1B,EAAI,EAAGA,EAAIf,EAAQxG,OAAQuH,QAE/B4B,GADAvJ,EAAU4G,EAAQe,IACNvH,OACZiJ,GAAK,KAASrJ,EAAQ,GAAKwW,EAAO,GAAKA,EAJhC,KAIkDxW,EAAQ,GAAKwW,EAAO,GAAKA,EAAO,KACpFtW,EAAI,EAAGA,EAAIqJ,EAAGrJ,IAClBmJ,IAAQrJ,EAAQE,GAAKsW,EAAO,GAAKA,EAN3B,IASJ1V,EAAK+T,MACR7B,EAAOlS,EAAK+T,OAASxL,EAErB2J,EAAO3O,aAAa,IAAKgF,IAI5BvI,EAAKgU,SAAWlO,GAAW9F,EAAKgU,QAAQ/R,KAAKjC,EAAKyU,OAAQ3O,EAASoM,IAEpE0D,0BACMD,IAAM5F,KAAK8E,SAAW,GAE5BgB,WDtqBM,SAASA,WAAW/X,OAGzBgI,EADGnE,GADJ7D,EAASD,EAAUC,IAAUI,EAAa0V,KAAK9V,IAAUgE,SAASgU,cAAchY,IAAkBA,GACpF8E,aAAe9E,EAAQ,SAEjC6D,IAAM7D,EAAQA,EAAM8E,aAAa,OAE/BjB,EAAEoU,UACNpU,EAAEoU,QAAU,KAEbjQ,EAAUnE,EAAEoU,QAAQjY,MACAgI,EAAQkQ,OAAUlQ,EAAWnE,EAAEoU,QAAQjY,GAAS4F,gBAAgB5F,IAE7EA,EAAgFD,EAAUC,GAAS4F,gBAAgB5F,GAAUgB,EAAUhB,EAAM,IAAO,CAACA,GAASA,EAAtJgK,QAAQmB,KAAK,0DC2pB7BvF,gBAAiBA,gBACjBF,gBAAiBA,gBACjByS,2CAAiBC,EAAQC,SAAS1L,IAAAA,WAAY4C,IAAAA,IACzC7E,EAAS,CAAC0N,EAAQC,UACtBrI,EAAYtF,EAAQiC,EAAY4C,GACzB7E,GAER4N,WAAYtI,EACZuI,aAAc/H,GACdgI,aAAc3M,EACd4M,wBAAyBrJ,EACzBrN,cAAe,yBAACuS,EAASrS,UAASoR,EAASiB,GAAS/E,IAAI,SAAA6E,UAAUrS,cAAcqS,GAAiB,IAATnS,MACxF4U,YAAa,SACbN,qBAAqB,EACrBP,WAAY,QAGbpL,KAAcC,EAAKE,eAAe8I"}