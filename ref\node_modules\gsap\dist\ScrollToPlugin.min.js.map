{"version": 3, "file": "ScrollToPlugin.min.js", "sources": ["../src/ScrollToPlugin.js"], "sourcesContent": ["/*!\n * ScrollToPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _window, _docEl, _body, _toArray, _config, ScrollTrigger,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isString = value => typeof(value) === \"string\",\n\t_isFunction = value => typeof(value) === \"function\",\n\t_max = (element, axis) => {\n\t\tlet dim = (axis === \"x\") ? \"Width\" : \"Height\",\n\t\t\tscroll = \"scroll\" + dim,\n\t\t\tclient = \"client\" + dim;\n\t\treturn (element === _window || element === _docEl || element === _body) ? Math.max(_docEl[scroll], _body[scroll]) - (_window[\"inner\" + dim] || _docEl[client] || _body[client]) : element[scroll] - element[\"offset\" + dim];\n\t},\n\t_buildGetter = (e, axis) => { //pass in an element and an axis (\"x\" or \"y\") and it'll return a getter function for the scroll position of that element (like scrollTop or scrollLeft, although if the element is the window, it'll use the pageXOffset/pageYOffset or the documentElement's scrollTop/scrollLeft or document.body's. Basically this streamlines things and makes a very fast getter across browsers.\n\t\tlet p = \"scroll\" + ((axis === \"x\") ? \"Left\" : \"Top\");\n\t\tif (e === _window) {\n\t\t\tif (e.pageXOffset != null) {\n\t\t\t\tp = \"page\" + axis.toUpperCase() + \"Offset\";\n\t\t\t} else {\n\t\t\t\te = _docEl[p] != null ? _docEl : _body;\n\t\t\t}\n\t\t}\n\t\treturn () => e[p];\n\t},\n\t_clean = (value, index, target, targets) => {\n\t\t_isFunction(value) && (value = value(index, target, targets));\n\t\tif (typeof(value) !== \"object\") {\n\t\t\treturn _isString(value) && value !== \"max\" && value.charAt(1) !== \"=\" ? {x: value, y: value} : {y: value}; //if we don't receive an object as the parameter, assume the user intends \"y\".\n\t\t} else if (value.nodeType) {\n\t\t\treturn {y: value, x: value};\n\t\t} else {\n\t\t\tlet result = {}, p;\n\t\t\tfor (p in value) {\n\t\t\t\tresult[p] = p !== \"onAutoKill\" && _isFunction(value[p]) ? value[p](index, target, targets) : value[p];\n\t\t\t}\n\t\t\treturn result;\n\t\t}\n\t},\n\t_getOffset = (element, container) => {\n\t\telement = _toArray(element)[0];\n\t\tif (!element || !element.getBoundingClientRect) {\n\t\t\treturn console.warn(\"scrollTo target doesn't exist. Using 0\") || {x:0, y:0};\n\t\t}\n\t\tlet rect = element.getBoundingClientRect(),\n\t\t\tisRoot = (!container || container === _window || container === _body),\n\t\t\tcRect = isRoot ? {top:_docEl.clientTop - (_window.pageYOffset || _docEl.scrollTop || _body.scrollTop || 0), left:_docEl.clientLeft - (_window.pageXOffset || _docEl.scrollLeft || _body.scrollLeft || 0)} : container.getBoundingClientRect(),\n\t\t\toffsets = {x: rect.left - cRect.left, y: rect.top - cRect.top};\n\t\tif (!isRoot && container) { //only add the current scroll position if it's not the window/body.\n\t\t\toffsets.x += _buildGetter(container, \"x\")();\n\t\t\toffsets.y += _buildGetter(container, \"y\")();\n\t\t}\n\t\treturn offsets;\n\t},\n\t_parseVal = (value, target, axis, currentVal, offset) => !isNaN(value) && typeof(value) !== \"object\" ? parseFloat(value) - offset : (_isString(value) && value.charAt(1) === \"=\") ? parseFloat(value.substr(2)) * (value.charAt(0) === \"-\" ? -1 : 1) + currentVal - offset : (value === \"max\") ? _max(target, axis) - offset : Math.min(_max(target, axis), _getOffset(value, target)[axis] - offset),\n\t_initCore = () => {\n\t\tgsap = _getGSAP();\n\t\tif (_windowExists() && gsap && typeof(document) !== \"undefined\" && document.body) {\n\t\t\t_window = window;\n\t\t\t_body = document.body;\n\t\t\t_docEl = document.documentElement;\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\tgsap.config({autoKillThreshold:7});\n\t\t\t_config = gsap.config();\n\t\t\t_coreInitted = 1;\n\t\t}\n\t};\n\n\nexport const ScrollToPlugin = {\n\tversion: \"3.13.0\",\n\tname: \"scrollTo\",\n\trawVars: 1,\n\tregister(core) {\n\t\tgsap = core;\n\t\t_initCore();\n\t},\n\tinit(target, value, tween, index, targets) {\n\t\t_coreInitted || _initCore();\n\t\tlet data = this,\n\t\t\tsnapType = gsap.getProperty(target, \"scrollSnapType\");\n\t\tdata.isWin = (target === _window);\n\t\tdata.target = target;\n\t\tdata.tween = tween;\n\t\tvalue = _clean(value, index, target, targets);\n\t\tdata.vars = value;\n\t\tdata.autoKill = !!(\"autoKill\" in value ? value : _config).autoKill;\n\t\tdata.getX = _buildGetter(target, \"x\");\n\t\tdata.getY = _buildGetter(target, \"y\");\n\t\tdata.x = data.xPrev = data.getX();\n\t\tdata.y = data.yPrev = data.getY();\n\t\tScrollTrigger || (ScrollTrigger = gsap.core.globals().ScrollTrigger);\n\t\tgsap.getProperty(target, \"scrollBehavior\") === \"smooth\" && gsap.set(target, {scrollBehavior: \"auto\"});\n\t\tif (snapType && snapType !== \"none\") { // disable scroll snapping to avoid strange behavior\n\t\t\tdata.snap = 1;\n\t\t\tdata.snapInline = target.style.scrollSnapType;\n\t\t\ttarget.style.scrollSnapType = \"none\";\n\t\t}\n\t\tif (value.x != null) {\n\t\t\tdata.add(data, \"x\", data.x, _parseVal(value.x, target, \"x\", data.x, value.offsetX || 0), index, targets);\n\t\t\tdata._props.push(\"scrollTo_x\");\n\t\t} else {\n\t\t\tdata.skipX = 1;\n\t\t}\n\t\tif (value.y != null) {\n\t\t\tdata.add(data, \"y\", data.y, _parseVal(value.y, target, \"y\", data.y, value.offsetY || 0), index, targets);\n\t\t\tdata._props.push(\"scrollTo_y\");\n\t\t} else {\n\t\t\tdata.skipY = 1;\n\t\t}\n\t},\n\trender(ratio, data) {\n\t\tlet pt = data._pt,\n\t\t\t{ target, tween, autoKill, xPrev, yPrev, isWin, snap, snapInline } = data,\n\t\t\tx, y, yDif, xDif, threshold;\n\t\twhile (pt) {\n\t\t\tpt.r(ratio, pt.d);\n\t\t\tpt = pt._next;\n\t\t}\n\t\tx = (isWin || !data.skipX) ? data.getX() : xPrev;\n\t\ty = (isWin || !data.skipY) ? data.getY() : yPrev;\n\t\tyDif = y - yPrev;\n\t\txDif = x - xPrev;\n\t\tthreshold = _config.autoKillThreshold;\n\t\tif (data.x < 0) { //can't scroll to a position less than 0! Might happen if someone uses a Back.easeOut or Elastic.easeOut when scrolling back to the top of the page (for example)\n\t\t\tdata.x = 0;\n\t\t}\n\t\tif (data.y < 0) {\n\t\t\tdata.y = 0;\n\t\t}\n\t\tif (autoKill) {\n\t\t\t//note: iOS has a bug that throws off the scroll by several pixels, so we need to check if it's within 7 pixels of the previous one that we set instead of just looking for an exact match.\n\t\t\tif (!data.skipX && (xDif > threshold || xDif < -threshold) && x < _max(target, \"x\")) {\n\t\t\t\tdata.skipX = 1; //if the user scrolls separately, we should stop tweening!\n\t\t\t}\n\t\t\tif (!data.skipY && (yDif > threshold || yDif < -threshold) && y < _max(target, \"y\")) {\n\t\t\t\tdata.skipY = 1; //if the user scrolls separately, we should stop tweening!\n\t\t\t}\n\t\t\tif (data.skipX && data.skipY) {\n\t\t\t\ttween.kill();\n\t\t\t\tdata.vars.onAutoKill && data.vars.onAutoKill.apply(tween, data.vars.onAutoKillParams || []);\n\t\t\t}\n\t\t}\n\t\tif (isWin) {\n\t\t\t_window.scrollTo((!data.skipX) ? data.x : x, (!data.skipY) ? data.y : y);\n\t\t} else {\n\t\t\tdata.skipY || (target.scrollTop = data.y);\n\t\t\tdata.skipX || (target.scrollLeft = data.x);\n\t\t}\n\t\tif (snap && (ratio === 1 || ratio === 0)) {\n\t\t\ty = target.scrollTop;\n\t\t\tx = target.scrollLeft;\n\t\t\tsnapInline ? (target.style.scrollSnapType = snapInline) : target.style.removeProperty(\"scroll-snap-type\");\n\t\t\ttarget.scrollTop = y + 1; // bug in Safari causes the element to totally reset its scroll position when scroll-snap-type changes, so we need to set it to a slightly different value and then back again to work around this bug.\n\t\t\ttarget.scrollLeft = x + 1;\n\t\t\ttarget.scrollTop = y;\n\t\t\ttarget.scrollLeft = x;\n\t\t}\n\t\tdata.xPrev = data.x;\n\t\tdata.yPrev = data.y;\n\t\tScrollTrigger && ScrollTrigger.update();\n\t},\n\tkill(property) {\n\t\tlet both = (property === \"scrollTo\"),\n\t\t\ti = this._props.indexOf(property);\n\t\tif (both || property === \"scrollTo_x\") {\n\t\t\tthis.skipX = 1;\n\t\t}\n\t\tif (both || property === \"scrollTo_y\") {\n\t\t\tthis.skipY = 1;\n\t\t}\n\t\ti > -1 && this._props.splice(i, 1);\n\t\treturn !this._props.length;\n\t}\n};\n\nScrollToPlugin.max = _max;\nScrollToPlugin.getOffset = _getOffset;\nScrollToPlugin.buildGetter = _buildGetter;\nScrollToPlugin.config = vars => {\n\t_config || _initCore() || (_config = gsap.config()); // in case the window hasn't been defined yet.\n\tfor (let p in vars) {\n\t\t_config[p] = vars[p];\n\t}\n}\n\n_getGSAP() && gsap.registerPlugin(ScrollToPlugin);\n\nexport { ScrollToPlugin as default };"], "names": ["_windowExists", "window", "_getGSAP", "gsap", "registerPlugin", "_isString", "value", "_isFunction", "_max", "element", "axis", "dim", "scroll", "client", "_window", "_docEl", "_body", "Math", "max", "_buildGetter", "e", "p", "pageXOffset", "toUpperCase", "_getOffset", "container", "_toArray", "getBoundingClientRect", "console", "warn", "x", "y", "rect", "isRoot", "cRect", "top", "clientTop", "pageYOffset", "scrollTop", "left", "clientLeft", "scrollLeft", "offsets", "_parseVal", "target", "currentVal", "offset", "isNaN", "char<PERSON>t", "parseFloat", "substr", "min", "_initCore", "document", "body", "documentElement", "utils", "toArray", "config", "autoKillThreshold", "_config", "_coreInitted", "ScrollTrigger", "ScrollToPlugin", "version", "name", "rawVars", "register", "core", "init", "tween", "index", "targets", "data", "this", "snapType", "getProperty", "isWin", "_clean", "nodeType", "result", "vars", "autoKill", "getX", "getY", "xPrev", "yPrev", "globals", "set", "scroll<PERSON>eh<PERSON>or", "snap", "snapInline", "style", "scrollSnapType", "add", "offsetX", "_props", "push", "skipX", "offsetY", "skipY", "render", "ratio", "yDif", "xDif", "threshold", "pt", "_pt", "r", "d", "_next", "kill", "onAutoKill", "apply", "onAutoKillParams", "scrollTo", "removeProperty", "update", "property", "both", "i", "indexOf", "splice", "length", "getOffset", "buildGetter"], "mappings": ";;;;;;;;;6MAWiB,SAAhBA,UAAyC,oBAAZC,OAClB,SAAXC,WAAiBC,GAASH,MAAoBG,EAAOF,OAAOE,OAASA,EAAKC,gBAAkBD,EAChF,SAAZE,EAAYC,SAA2B,iBAAXA,EACd,SAAdC,EAAcD,SAA2B,mBAAXA,EACvB,SAAPE,EAAQC,EAASC,OACZC,EAAgB,MAATD,EAAgB,QAAU,SACpCE,EAAS,SAAWD,EACpBE,EAAS,SAAWF,SACbF,IAAYK,GAAWL,IAAYM,GAAUN,IAAYO,EAASC,KAAKC,IAAIH,EAAOH,GAASI,EAAMJ,KAAYE,EAAQ,QAAUH,IAAQI,EAAOF,IAAWG,EAAMH,IAAWJ,EAAQG,GAAUH,EAAQ,SAAWE,GAEzM,SAAfQ,EAAgBC,EAAGV,OACdW,EAAI,UAAsB,MAATX,EAAgB,OAAS,cAC1CU,IAAMN,IACY,MAAjBM,EAAEE,YACLD,EAAI,OAASX,EAAKa,cAAgB,SAElCH,EAAiB,MAAbL,EAAOM,GAAaN,EAASC,GAG5B,kBAAMI,EAAEC,IAgBH,SAAbG,EAAcf,EAASgB,QACtBhB,EAAUiB,EAASjB,GAAS,MACXA,EAAQkB,6BACjBC,QAAQC,KAAK,2CAA6C,CAACC,EAAE,EAAGC,EAAE,OAEtEC,EAAOvB,EAAQkB,wBAClBM,GAAWR,GAAaA,IAAcX,GAAWW,IAAcT,EAC/DkB,EAAQD,EAAS,CAACE,IAAIpB,EAAOqB,WAAatB,EAAQuB,aAAetB,EAAOuB,WAAatB,EAAMsB,WAAa,GAAIC,KAAKxB,EAAOyB,YAAc1B,EAAQQ,aAAeP,EAAO0B,YAAczB,EAAMyB,YAAc,IAAMhB,EAAUE,wBACtNe,EAAU,CAACZ,EAAGE,EAAKO,KAAOL,EAAMK,KAAMR,EAAGC,EAAKG,IAAMD,EAAMC,YACtDF,GAAUR,IACdiB,EAAQZ,GAAKX,EAAaM,EAAW,IAAxBN,GACbuB,EAAQX,GAAKZ,EAAaM,EAAW,IAAxBN,IAEPuB,EAEI,SAAZC,EAAarC,EAAOsC,EAAQlC,EAAMmC,EAAYC,UAAYC,MAAMzC,IAA4B,iBAAXA,EAAoDD,EAAUC,IAA8B,MAApBA,EAAM0C,OAAO,GAAcC,WAAW3C,EAAM4C,OAAO,KAA2B,MAApB5C,EAAM0C,OAAO,IAAc,EAAI,GAAKH,EAAaC,EAAoB,QAAVxC,EAAmBE,EAAKoC,EAAQlC,GAAQoC,EAAS7B,KAAKkC,IAAI3C,EAAKoC,EAAQlC,GAAOc,EAAWlB,EAAOsC,GAAQlC,GAAQoC,GAAvRG,WAAW3C,GAASwC,EAC/G,SAAZM,IACCjD,EAAOD,IACHF,KAAmBG,GAA6B,oBAAdkD,UAA6BA,SAASC,OAC3ExC,EAAUb,OACVe,EAAQqC,SAASC,KACjBvC,EAASsC,SAASE,gBAClB7B,EAAWvB,EAAKqD,MAAMC,QACtBtD,EAAKuD,OAAO,CAACC,kBAAkB,IAC/BC,EAAUzD,EAAKuD,SACfG,EAAe,GA7DlB,IAAI1D,EAAM0D,EAAc/C,EAASC,EAAQC,EAAOU,EAAUkC,EAASE,EAkEtDC,EAAiB,CAC7BC,QAAS,SACTC,KAAM,WACNC,QAAS,EACTC,2BAASC,GACRjE,EAAOiE,EACPhB,KAEDiB,mBAAKzB,EAAQtC,EAAOgE,EAAOC,EAAOC,GACjCX,GAAgBT,QACZqB,EAAOC,KACVC,EAAWxE,EAAKyE,YAAYhC,EAAQ,kBACrC6B,EAAKI,MAASjC,IAAW9B,EACzB2D,EAAK7B,OAASA,EACd6B,EAAKH,MAAQA,EACbhE,EA3DQ,SAATwE,OAAUxE,EAAOiE,EAAO3B,EAAQ4B,MAC/BjE,EAAYD,KAAWA,EAAQA,EAAMiE,EAAO3B,EAAQ4B,IAC9B,iBAAXlE,SACHD,EAAUC,IAAoB,QAAVA,GAAuC,MAApBA,EAAM0C,OAAO,GAAa,CAAClB,EAAGxB,EAAOyB,EAAGzB,GAAS,CAACyB,EAAGzB,GAC7F,GAAIA,EAAMyE,eACT,CAAChD,EAAGzB,EAAOwB,EAAGxB,OAEJe,EAAb2D,EAAS,OACR3D,KAAKf,EACT0E,EAAO3D,GAAW,eAANA,GAAsBd,EAAYD,EAAMe,IAAMf,EAAMe,GAAGkD,EAAO3B,EAAQ4B,GAAWlE,EAAMe,UAE7F2D,EAgDAF,CAAOxE,EAAOiE,EAAO3B,EAAQ4B,GACrCC,EAAKQ,KAAO3E,EACZmE,EAAKS,YAAc,aAAc5E,EAAQA,EAAQsD,GAASsB,SAC1DT,EAAKU,KAAOhE,EAAayB,EAAQ,KACjC6B,EAAKW,KAAOjE,EAAayB,EAAQ,KACjC6B,EAAK3C,EAAI2C,EAAKY,MAAQZ,EAAKU,OAC3BV,EAAK1C,EAAI0C,EAAKa,MAAQb,EAAKW,OACTtB,EAAlBA,GAAkC3D,EAAKiE,KAAKmB,UAAUzB,cACP,WAA/C3D,EAAKyE,YAAYhC,EAAQ,mBAAkCzC,EAAKqF,IAAI5C,EAAQ,CAAC6C,eAAgB,SACzFd,GAAyB,SAAbA,IACfF,EAAKiB,KAAO,EACZjB,EAAKkB,WAAa/C,EAAOgD,MAAMC,eAC/BjD,EAAOgD,MAAMC,eAAiB,QAEhB,MAAXvF,EAAMwB,GACT2C,EAAKqB,IAAIrB,EAAM,IAAKA,EAAK3C,EAAGa,EAAUrC,EAAMwB,EAAGc,EAAQ,IAAK6B,EAAK3C,EAAGxB,EAAMyF,SAAW,GAAIxB,EAAOC,GAChGC,EAAKuB,OAAOC,KAAK,eAEjBxB,EAAKyB,MAAQ,EAEC,MAAX5F,EAAMyB,GACT0C,EAAKqB,IAAIrB,EAAM,IAAKA,EAAK1C,EAAGY,EAAUrC,EAAMyB,EAAGa,EAAQ,IAAK6B,EAAK1C,EAAGzB,EAAM6F,SAAW,GAAI5B,EAAOC,GAChGC,EAAKuB,OAAOC,KAAK,eAEjBxB,EAAK2B,MAAQ,GAGfC,uBAAOC,EAAO7B,WAGZ3C,EAAGC,EAAGwE,EAAMC,EAAMC,EAFfC,EAAKjC,EAAKkC,IACX/D,EAAmE6B,EAAnE7B,OAAQ0B,EAA2DG,EAA3DH,MAAOY,EAAoDT,EAApDS,SAAUG,EAA0CZ,EAA1CY,MAAOC,EAAmCb,EAAnCa,MAAOT,EAA4BJ,EAA5BI,MAAOa,EAAqBjB,EAArBiB,KAAMC,EAAelB,EAAfkB,WAEhDe,GACNA,EAAGE,EAAEN,EAAOI,EAAGG,GACfH,EAAKA,EAAGI,MAEThF,EAAK+C,IAAUJ,EAAKyB,MAASzB,EAAKU,OAASE,EAE3CkB,GADAxE,EAAK8C,IAAUJ,EAAK2B,MAAS3B,EAAKW,OAASE,GAChCA,EACXkB,EAAO1E,EAAIuD,EACXoB,EAAY7C,EAAQD,kBAChBc,EAAK3C,EAAI,IACZ2C,EAAK3C,EAAI,GAEN2C,EAAK1C,EAAI,IACZ0C,EAAK1C,EAAI,GAENmD,KAEET,EAAKyB,QAAiBO,EAAPD,GAAoBA,GAAQC,IAAc3E,EAAItB,EAAKoC,EAAQ,OAC9E6B,EAAKyB,MAAQ,IAETzB,EAAK2B,QAAiBK,EAAPF,GAAoBA,GAAQE,IAAc1E,EAAIvB,EAAKoC,EAAQ,OAC9E6B,EAAK2B,MAAQ,GAEV3B,EAAKyB,OAASzB,EAAK2B,QACtB9B,EAAMyC,OACNtC,EAAKQ,KAAK+B,YAAcvC,EAAKQ,KAAK+B,WAAWC,MAAM3C,EAAOG,EAAKQ,KAAKiC,kBAAoB,MAGtFrC,EACH/D,EAAQqG,SAAW1C,EAAKyB,MAAkBpE,EAAT2C,EAAK3C,EAAS2C,EAAK2B,MAAkBrE,EAAT0C,EAAK1C,IAElE0C,EAAK2B,QAAUxD,EAAON,UAAYmC,EAAK1C,GACvC0C,EAAKyB,QAAUtD,EAAOH,WAAagC,EAAK3C,KAErC4D,GAAmB,IAAVY,GAAyB,IAAVA,IAC3BvE,EAAIa,EAAON,UACXR,EAAIc,EAAOH,WACXkD,EAAc/C,EAAOgD,MAAMC,eAAiBF,EAAc/C,EAAOgD,MAAMwB,eAAe,oBACtFxE,EAAON,UAAYP,EAAI,EACvBa,EAAOH,WAAaX,EAAI,EACxBc,EAAON,UAAYP,EACnBa,EAAOH,WAAaX,GAErB2C,EAAKY,MAAQZ,EAAK3C,EAClB2C,EAAKa,MAAQb,EAAK1C,EAClB+B,GAAiBA,EAAcuD,UAEhCN,mBAAKO,OACAC,EAAqB,aAAbD,EACXE,EAAI9C,KAAKsB,OAAOyB,QAAQH,UACrBC,GAAqB,eAAbD,SACNpB,MAAQ,IAEVqB,GAAqB,eAAbD,SACNlB,MAAQ,IAET,EAALoB,GAAU9C,KAAKsB,OAAO0B,OAAOF,EAAG,IACxB9C,KAAKsB,OAAO2B,SAItB5D,EAAe7C,IAAMV,EACrBuD,EAAe6D,UAAYpG,EAC3BuC,EAAe8D,YAAc1G,EAC7B4C,EAAeL,OAAS,SAAAuB,OAElB,IAAI5D,KADTuC,GAAWR,MAAgBQ,EAAUzD,EAAKuD,UAC5BuB,EACbrB,EAAQvC,GAAK4D,EAAK5D,IAIpBnB,KAAcC,EAAKC,eAAe2D"}